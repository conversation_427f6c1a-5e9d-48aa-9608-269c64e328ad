# IrrigaNet - Technical Documentation

## Overview

IrrigaNet is an Android mobile application designed for configuring and monitoring Localized Irrigation Controllers (LIC) in agricultural irrigation systems. The application serves as a simplified configuration tool that communicates with irrigation hardware via MQTT protocol using Protocol Buffers (protobuf) for message serialization.

## Structure & Architecture

### Directory Structure

```
irriganet/
├── app/                           # Main Android application
│   ├── build.gradle              # Android build configuration
│   ├── proguard-rules.pro        # ProGuard configuration
│   └── src/main/
│       ├── AndroidManifest.xml   # App manifest
│       ├── java/br/com/byagro/irriganet/  # Kotlin source code
│       ├── proto/                 # Protocol Buffer definitions
│       └── res/                   # Android resources
├── build.gradle                   # Project-level build configuration
├── gradle/                        # Gradle wrapper and dependencies
├── gradle.properties             # Gradle properties
├── gradlew                       # Gradle wrapper script (Unix)
├── gradlew.bat                   # Gradle wrapper script (Windows)
└── settings.gradle               # Gradle settings
```

### Module Hierarchy

The application follows Android's standard architecture with these key components:

- **MainActivity.kt**: Central coordinator managing MQTT communication and navigation
- **UI Fragments**: Modular screens for different functionalities
- **DBHelper.kt**: SQLite database management layer
- **MqttManager.kt**: MQTT client wrapper using HiveMQ library
- **WebService.kt**: HTTP API communication utilities
- **SharedData.kt**: Global state management

### Entry Points

- **Main Entry**: `MainActivity` - Launched activity with navigation drawer
- **Navigation**: Fragment-based navigation using Android Navigation Component
- **Default Screen**: Report fragment (`nav_report`) showing system overview

## Technical Implementation

### Programming Languages & Frameworks

- **Primary Language**: Kotlin for Android
- **UI Framework**: Android Jetpack with Navigation Component
- **Database**: SQLite with custom ORM (DBHelper)
- **MQTT Client**: HiveMQ MQTT Client v1.3.7
- **Serialization**: Google Protocol Buffers v3.25.3
- **Async Processing**: Kotlin Coroutines
- **HTTP Client**: Android Volley v1.2.1

### Key Dependencies

```gradle
// Core Android
implementation libs.androidx.core.ktx
implementation libs.androidx.appcompat
implementation libs.material
implementation libs.androidx.constraintlayout

// Navigation
implementation libs.androidx.navigation.fragment.ktx
implementation libs.androidx.navigation.ui.ktx

// MQTT Communication
implementation("com.hivemq:hivemq-mqtt-client:1.3.7")

// Protocol Buffers
implementation 'com.google.protobuf:protobuf-kotlin-lite:3.25.3'

// JSON Processing
implementation 'com.google.code.gson:gson:2.8.9'

// HTTP Client
implementation 'com.android.volley:volley:1.2.1'

// Coroutines
implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.8.0")
implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.0")

// CBOR Serialization
implementation("com.upokecenter:cbor:4.5.2")
```

### Build System Configuration

- **Build Tool**: Gradle with Android Gradle Plugin
- **Compile SDK**: 35 (Android 14)
- **Target SDK**: 35
- **Minimum SDK**: 24 (Android 7.0)
- **Java Version**: 11
- **Kotlin JVM Target**: 11
- **Protobuf Plugin**: v0.9.5 for automatic code generation

### Database Schema

The application uses SQLite with 7 main tables:

1. **Codecs**: LIC device registry with WiFi credentials
2. **Groups**: Logical groupings of irrigation devices
3. **Mesh_Devices**: Mesh network device definitions
4. **Devices**: Individual device endpoints with operational parameters
5. **Schedulings**: Irrigation schedule definitions
6. **Device_Schedulings**: Device-specific scheduling steps
7. **Sector_Schedulings**: Sector-based scheduling configurations

## Communication & Protocols

### MQTT Communication Architecture

**Broker Configuration**:

- **Host**: `mosquitto-codec.saas.byagro.dev.br`
- **Port**: 8003
- **Protocol**: MQTT v3.1.1 and v5.0 support
- **Authentication**: Username/password (`codec` / base64 encoded)
- **Client ID**: `IrrigaNet-{UUID}` for unique identification

**Topic Structure**:

```
/codec/{device_identifier}/report    # Uplink messages (FROM device)
/codec/{device_identifier}/downlink  # Downlink messages (TO device)
```

### Protocol Buffer Message Definitions

#### Incoming Messages (TO LIC Devices)

**Container Message**:

```protobuf
message IncomingPacket {
  uint64 id = 1;
  oneof payload {
    codec.in.config.ConfigPackage config = 2;
    codec.in.devices.DevicesPackage devices = 3;
    codec.in.scheduling.SchedulingPackage scheduling = 4;
    codec.in.device_scheduling.DeviceSchedulingPackage dev_scheduling = 5;
    codec.in.automation.AutomationPackage automation = 6;
    codec.in.control.ControlPackage control = 7;
    codec.in.pause.PauseSchedulingPackage pause = 8;
    codec.in.request_info.RequestInfoPackage request_info = 9;
    codec.in.firmware_update.FirmwareUpdatePackage firmware_update = 10;
  }
}
```

#### Outgoing Messages (FROM LIC Devices)

**Container Message**:

```protobuf
message OutgoingPacket {
  uint64 id = 1;
  oneof payload {
    codec.out.info.InfoPackage info = 2;
    codec.out.status.SystemStatusPackage status = 3;
    codec.out.scheduling_report.SchedulingReportPackage scheduling_report = 4;
    codec.out.automation_report.AutomationReportPackage automation_report = 5;
    codec.out.ack.AckPackage ack = 6;
  }
}
```

### Message Exchange Mechanisms

1. **Configuration Updates**: App sends device/scheduling configurations
2. **Status Monitoring**: Devices report operational status and sensor data
3. **Control Commands**: Real-time device control (start/stop irrigation)
4. **Firmware Updates**: OTA firmware update coordination
5. **Acknowledgments**: Confirmation of received and processed messages

## User Interaction

### User Interface Architecture

**Navigation Pattern**: Material Design Navigation Drawer with Fragment-based screens

**Main Screens**:

1. **Report (Visão Geral)**: System overview and status dashboard
2. **Irrigation (Irrigação)**: Irrigation scheduling and group management
3. **Individual Pumps (Bombas)**: Service pump control interface
4. **Configuration**: System settings and device management

### User Workflows

#### Device Registration Workflow

1. Navigate to Codec Registration (`nav_codec_reg`)
2. Enter device identity, name, and WiFi credentials
3. System stores codec information and establishes MQTT subscription
4. Device appears in system overview

#### Irrigation Configuration Workflow

1. Access Irrigation screen
2. Create/modify irrigation groups
3. Configure scheduling parameters
4. Deploy configuration to LIC devices via MQTT
5. Monitor execution status

#### Device Control Workflow

1. Select device from device manager
2. Access device control interface
3. Send real-time control commands (start/stop)
4. Monitor device response and status updates

### Authentication & Authorization

- **Local Authentication**: No centralized authentication system
- **Device Access**: Direct MQTT connection to configured devices
- **Security**: WiFi credentials stored locally in SQLite database
- **Network Security**: MQTT over TLS (implied by broker configuration)

### Configuration Options

**Application Settings**:

- Rain gauge configuration (enabled/disabled, sensitivity)
- Backwash cycle parameters
- WiFi network configuration for devices
- Mesh network encryption settings
- Rainfall limits and pause durations

## Data Flow & Processing

### Message Processing Pipeline

1. **MQTT Message Reception**: HiveMqttManager receives binary messages
2. **Protocol Buffer Deserialization**: Messages parsed using generated protobuf classes
3. **Database Updates**: Relevant data stored/updated in SQLite database
4. **UI Updates**: LiveData/ViewModel pattern updates UI components
5. **User Feedback**: Status indicators and notifications

### Message Serialization/Deserialization

- **Outbound**: Kotlin objects → Protobuf binary → MQTT publish
- **Inbound**: MQTT message → Protobuf binary → Kotlin objects → Database/UI
- **Error Handling**: CRC validation and message integrity checks
- **Logging**: Comprehensive MQTT message logging with hex dump

### Error Handling & Logging

**Error Categories**:

- MQTT connection failures with automatic reconnection
- Protobuf parsing errors with graceful degradation
- Database operation failures with transaction rollback
- Network timeouts with retry mechanisms

**Logging Strategy**:

- MQTT message traffic logging with hex representation
- Database operation logging
- UI interaction logging
- Error stack trace capture

### Performance Considerations

- **Background Processing**: MQTT handling on IO dispatcher
- **Database Optimization**: Indexed queries and prepared statements
- **Memory Management**: Efficient protobuf object lifecycle
- **Battery Optimization**: Optimized MQTT keepalive intervals

## Integration Points

### External System Integrations

**MQTT Broker Integration**:

- Mosquitto broker hosted at `mosquitto-codec.saas.byagro.dev.br`
- Supports multiple concurrent client connections
- Message persistence and QoS level management

**Web Service Integration**:

- HTTP API endpoints for additional data synchronization
- JSON-based communication using Volley HTTP client
- Base64 encoding for binary data transmission

### Hardware Interface Patterns

**LIC Device Communication**:

- ESP32-based irrigation controllers
- Mesh networking capability for extended range
- Multiple I/O channels for sensors and actuators
- Firmware update capability via MQTT

**Sensor Integration**:

- Rain gauge sensors with configurable sensitivity
- Soil moisture sensors for automation triggers
- Flow sensors for irrigation monitoring
- Level sensors for reservoir management

### Database Integration

**Local Data Persistence**:

- SQLite database with foreign key constraints
- Automatic schema migration support
- Concurrent access handling with proper locking
- Data export/import capabilities for backup

**Data Synchronization**:

- Timestamp-based change detection
- Incremental updates to minimize bandwidth
- Conflict resolution for concurrent modifications
- Offline operation support with sync on reconnection

## Related System Components

### MQTT Integration Service

The broader system includes a Node.js/TypeScript MQTT integration service (`mqtt-integration/`) that:

- Processes MQTT messages from multiple LIC devices
- Integrates with the main Directus-based backend system
- Handles message queuing and reliable delivery
- Provides comprehensive logging and monitoring

### Protobuf Definitions

Shared protobuf definitions (`protobuf/`) used across:

- Android application (this component)
- MQTT integration service
- Backend API systems
- Hardware firmware implementations

### Database Integration

The Android app operates independently but shares conceptual models with the main system's PostgreSQL database managed through Directus CMS, enabling future integration and data synchronization capabilities.

## Complete SQLite Database Schema Documentation

### Database Configuration

- **Database Name**: `irriganet.db`
- **Database Version**: 55
- **Foreign Key Constraints**: Enabled
- **Migration Strategy**: Drop and recreate all tables on version upgrade

### Table Definitions

#### 1. Codecs Table

**Purpose**: Registry of LIC (Localized Irrigation Controller) devices with connection credentials and synchronization timestamps.

```sql
CREATE TABLE Codecs (
    idx INTEGER PRIMARY KEY AUTOINCREMENT,
    identity TEXT NOT NULL,                    -- Unique device identifier/serial number
    name TEXT NOT NULL,                        -- Human-readable device name
    wifi_ssid TEXT,                           -- WiFi network SSID for device connection
    wifi_passwd TEXT,                         -- WiFi network password
    last_devices_update INTEGER,              -- Timestamp of last device configuration update
    last_scheduling_update INTEGER,           -- Timestamp of last scheduling update
    last_device_scheduling_update INTEGER,    -- Timestamp of last device scheduling update
    last_automation_update INTEGER,           -- Timestamp of last automation update
    last_config_update INTEGER,               -- Timestamp of last general configuration update
    enabled INTEGER DEFAULT 1                 -- Device enabled status (1=enabled, 0=disabled)
)
```

**Business Rules**:

- `identity` serves as the unique device identifier used in MQTT topics
- Timestamp fields track when each configuration type was last sent to the device
- Used for change detection to avoid unnecessary MQTT message transmission

#### 2. Groups Table

**Purpose**: Logical groupings of irrigation devices for coordinated scheduling and management.

```sql
CREATE TABLE Groups (
    idx INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,                       -- Group display name
    codec_idx INTEGER,                        -- Reference to parent codec
    FOREIGN KEY (codec_idx) REFERENCES Codecs(idx) ON DELETE CASCADE
)
```

**Indexes**:

```sql
CREATE INDEX idx_groups_codec_idx ON Groups(codec_idx)
```

**Business Rules**:

- Groups belong to a specific codec/LIC device
- Used to organize devices for batch scheduling operations
- Cascade delete ensures cleanup when codec is removed

#### 3. Mesh_Devices Table

**Purpose**: Mesh network device definitions representing physical irrigation hardware connected via mesh networking.

```sql
CREATE TABLE Mesh_Devices (
    idx INTEGER PRIMARY KEY AUTOINCREMENT,
    identity TEXT NOT NULL,                   -- Mesh device identifier
    name TEXT NOT NULL,                       -- Device display name
    type INTEGER DEFAULT 0,                   -- Device type (valve, pump, sensor, etc.)
    mode INTEGER DEFAULT 0,                   -- Operating mode
    equipament INTEGER DEFAULT 0,             -- Equipment type/version
    check_input INTEGER DEFAULT 0,            -- Input monitoring enabled flag
    devices_bitmask INTEGER DEFAULT 0,        -- Bitmask of connected sub-devices
    level_pump_idx INTEGER DEFAULT 0,         -- Associated level sensor index
    level_pump_enable INTEGER DEFAULT 0,      -- Level-based pump control enabled
    level_pump_working_time INTEGER DEFAULT 0, -- Pump operation duration (minutes)
    codec_idx INTEGER,                        -- Parent codec reference
    group_idx INTEGER,                        -- Parent group reference
    FOREIGN KEY (codec_idx) REFERENCES Codecs(idx) ON DELETE CASCADE,
    FOREIGN KEY (group_idx) REFERENCES Groups(idx) ON DELETE CASCADE
)
```

**Indexes**:

```sql
CREATE INDEX idx_mesh_devices_codec_idx ON Mesh_Devices(codec_idx)
CREATE INDEX idx_mesh_devices_group_idx ON Mesh_Devices(group_idx)
```

**Business Rules**:

- Represents physical mesh network nodes
- Supports automation through level sensor integration
- Bitmask field tracks which sub-devices are active

#### 4. Devices Table

**Purpose**: Individual device endpoints with operational parameters and sector assignments.

```sql
CREATE TABLE Devices (
    idx INTEGER PRIMARY KEY AUTOINCREMENT,
    ord_idx INTEGER NOT NULL,                 -- Ordered index for device sequencing
    mesh_idx INTEGER NOT NULL,                -- Parent mesh device reference
    identity TEXT NOT NULL,                   -- Device endpoint identifier
    type INTEGER NOT NULL,                    -- Device type code
    out1 INTEGER,                            -- Output 1 configuration
    out2 INTEGER,                            -- Output 2 configuration
    input INTEGER,                           -- Input configuration
    mode INTEGER,                            -- Device operating mode
    sector INTEGER,                          -- Irrigation sector assignment
    eqpt_ver INTEGER,                        -- Equipment version
    FOREIGN KEY (mesh_idx) REFERENCES Mesh_Devices(idx) ON DELETE CASCADE
)
```

**Indexes**:

```sql
CREATE INDEX idx_devices_mesh_idx ON Devices(mesh_idx)
CREATE INDEX idx_devices_type ON Devices(type)
CREATE INDEX idx_devices_sector ON Devices(sector)
CREATE UNIQUE INDEX u_mesh_identity ON Devices(mesh_idx, identity)
```

**Business Rules**:

- Each device belongs to a mesh device and has a unique identity within that mesh
- `ord_idx` determines execution order in scheduling
- Sector assignment enables zone-based irrigation control

#### 5. Schedulings Table

**Purpose**: Irrigation schedule definitions with timing, duration, and feature settings.

```sql
CREATE TABLE Schedulings (
    idx INTEGER PRIMARY KEY AUTOINCREMENT,
    ord_idx INTEGER NOT NULL,                 -- Execution order index
    group_idx INTEGER NOT NULL,               -- Parent group reference
    name TEXT NOT NULL,                       -- Schedule display name
    hour INTEGER NOT NULL,                    -- Start hour (0-23)
    min INTEGER NOT NULL,                     -- Start minute (0-59)
    start_time INTEGER NOT NULL,              -- Start time in minutes since midnight
    end_time INTEGER DEFAULT NULL,            -- End time in minutes since midnight
    days_of_week INTEGER NOT NULL,            -- Days bitmask (bit 0=Sunday, bit 6=Saturday)
    number_of_steps INTEGER NOT NULL,         -- Total number of irrigation steps
    allow_ferti INTEGER NOT NULL,             -- Fertigation enabled (1=yes, 0=no)
    allow_backwash INTEGER NOT NULL,          -- Backwash enabled (1=yes, 0=no)
    waterpump_idx INTEGER DEFAULT NULL,       -- Water pump device index
    waterpump_ord_idx INTEGER DEFAULT NULL,   -- Water pump order index
    waterpump_working_time INTEGER DEFAULT NULL, -- Pump operation time (minutes)
    ferti_idx INTEGER DEFAULT NULL,           -- Fertigation device index
    ferti_ord_idx INTEGER DEFAULT NULL,       -- Fertigation order index
    backwash_idx INTEGER DEFAULT NULL,        -- Backwash device index
    backwash_ord_idx INTEGER DEFAULT NULL,    -- Backwash order index
    enabled INTEGER DEFAULT 1,                -- Schedule enabled status
    FOREIGN KEY (group_idx) REFERENCES Groups(idx) ON DELETE CASCADE
)
```

**Indexes**:

```sql
CREATE INDEX idx_schedulings_group_idx ON Schedulings(group_idx)
```

**Business Rules**:

- Schedules belong to device groups
- Time stored both as hour/minute and total minutes for flexibility
- Bitmask encoding for days of week (0b01111111 = all days)
- Optional features (fertigation, backwash) controlled by separate flags

#### 6. Sector_Schedulings Table

**Purpose**: Sector-based scheduling steps with fertigation and timing parameters.

```sql
CREATE TABLE Sector_Schedulings (
    idx INTEGER PRIMARY KEY AUTOINCREMENT,
    scheduling_idx INTEGER NOT NULL,          -- Parent schedule reference
    device_idx INTEGER NOT NULL,             -- Target device reference
    n_order INTEGER NOT NULL,                -- Execution order within schedule
    enabled INTEGER NOT NULL,                -- Step enabled status
    type INTEGER NOT NULL,                   -- Step type (irrigation, fertigation, etc.)
    ferti INTEGER NOT NULL,                  -- Fertigation enabled for this step
    ferti_delay INTEGER NOT NULL,            -- Fertigation start delay (minutes)
    working_time INTEGER NOT NULL,           -- Step duration (minutes)
    FOREIGN KEY (scheduling_idx) REFERENCES Schedulings(idx) ON DELETE CASCADE
)
```

**Indexes**:

```sql
CREATE INDEX idx_sector_schedulings_scheduling_idx ON Sector_Schedulings(scheduling_idx)
CREATE INDEX idx_sector_schedulings_device_idx ON Sector_Schedulings(device_idx)
```

**Business Rules**:

- Defines sector-level irrigation steps within a schedule
- Execution order determined by `n_order` field
- Supports per-step fertigation control with configurable delay

#### 7. Device_Schedulings Table

**Purpose**: Device-specific scheduling steps with detailed timing and fertigation parameters.

```sql
CREATE TABLE Device_Schedulings (
    idx INTEGER PRIMARY KEY AUTOINCREMENT,
    ord_idx INTEGER NOT NULL,                 -- Ordered index for sequencing
    scheduling_idx INTEGER NOT NULL,          -- Parent schedule reference
    device_idx INTEGER NOT NULL,             -- Target device reference
    n_order INTEGER NOT NULL,                -- Execution order within schedule
    status INTEGER NOT NULL,                 -- Step status
    type INTEGER NOT NULL,                   -- Step type
    time INTEGER NOT NULL,                   -- Step start time offset (minutes)
    sector_working_time INTEGER NOT NULL,    -- Sector operation duration (minutes)
    ferti_working_time INTEGER DEFAULT NULL, -- Fertigation duration (minutes)
    ferti_delay INTEGER NOT NULL,            -- Fertigation start delay (minutes)
    FOREIGN KEY (scheduling_idx) REFERENCES Schedulings(idx) ON DELETE CASCADE
)
```

**Indexes**:

```sql
CREATE INDEX idx_device_schedulings_scheduling_idx ON Device_Schedulings(scheduling_idx)
CREATE INDEX idx_device_schedulings_device_idx ON Device_Schedulings(device_idx)
```

**Business Rules**:

- Provides device-level granularity for scheduling
- Time offsets allow precise coordination between devices
- Separate timing for sector operation and fertigation
- Status field tracks execution state

## Complete Protocol Buffer Message Documentation

### Message Flow Direction

- **Downlink Messages (TO LIC)**: Sent from Android app to irrigation devices via MQTT
- **Uplink Messages (FROM LIC)**: Sent from irrigation devices to Android app via MQTT

### Downlink Messages (IncomingPacket Container)

All downlink messages are wrapped in the `IncomingPacket` container:

```protobuf
message IncomingPacket {
  uint64 id = 1;                                    // Message timestamp/ID for tracking
  oneof payload {
    codec.in.config.ConfigPackage config = 2;
    codec.in.devices.DevicesPackage devices = 3;
    codec.in.scheduling.SchedulingPackage scheduling = 4;
    codec.in.device_scheduling.DeviceSchedulingPackage dev_scheduling = 5;
    codec.in.automation.AutomationPackage automation = 6;
    codec.in.control.ControlPackage control = 7;
    codec.in.pause.PauseSchedulingPackage pause = 8;
    codec.in.request_info.RequestInfoPackage request_info = 9;
    codec.in.firmware_update.FirmwareUpdatePackage firmware_update = 10;
  }
}
```

#### 1. ConfigPackage - System Configuration

**Purpose**: Configure LIC device system settings including WiFi, mesh networking, and irrigation parameters.

```protobuf
message ConfigPackage {
  int32 backwash_cycle = 1;                 // Backwash cycle number
  int32 backwash_duration = 2;              // Backwash duration (minutes)
  int32 backwash_delay = 3;                 // Delay before backwash (minutes)
  bool raingauge_enabled = 4;               // Rain gauge sensor enabled
  int32 raingauge_factor = 5;               // Rain gauge resolution factor
  int32 rainfall_limit = 6;                 // Rainfall limit threshold (mm)
  int32 rainfall_pause_duration = 7;        // Pause duration after rain (minutes)
  WifiConfig wifi = 8;                      // WiFi network configuration
  MeshConfig mesh = 9;                      // Mesh network configuration
}

message WifiConfig {
  string ssid = 1;                          // WiFi network SSID
  string password = 2;                      // WiFi network password
}

message MeshConfig {
  bytes key = 1;                            // Mesh network encryption key
  uint32 channel = 2;                       // Mesh network channel
}
```

**Field Descriptions**:

- `backwash_cycle`: Number of irrigation cycles before automatic backwash
- `raingauge_factor`: Pulses per millimeter for rain gauge calibration
- `rainfall_limit`: Minimum rainfall to trigger irrigation pause
- `wifi`: Network credentials for internet connectivity
- `mesh`: Mesh network settings for device-to-device communication

#### 2. DevicesPackage - Device Configuration

**Purpose**: Configure mesh network devices and their operational parameters.

```protobuf
message DevicesPackage {
  repeated DevicesData data = 1;            // List of device configurations
}

message DevicesData {
  int32 idx = 1;                           // Device slot index in array
  int32 mesh_id = 2;                       // Mesh network device ID
  int32 device_id = 3;                     // Unique device identifier
  int32 device_type = 4;                   // Device type code
  int32 out1 = 5;                          // Output 1 state/configuration
  int32 out2 = 6;                          // Output 2 state/configuration
  int32 input = 7;                         // Input state/configuration
  int32 mode = 8;                          // Device operating mode
  int32 sector = 9;                        // Irrigation sector assignment
  int32 group_idx = 10;                    // Group index for organization
  int32 eqpt_ver = 11;                     // Equipment version
}
```

**Field Descriptions**:

- `idx`: Position in device array for ordered operations
- `mesh_id`: Identifies device within mesh network
- `device_type`: Valve, pump, sensor, or other device types
- `out1/out2`: Digital output configurations for device control
- `input`: Digital input configuration for sensor reading
- `sector`: Irrigation zone assignment for scheduling

#### 3. SchedulingPackage - Irrigation Scheduling

**Purpose**: Define irrigation schedules with timing and device coordination.

```protobuf
message SchedulingPackage {
  MsgType type = 1;                         // Message content type
  repeated Scheduling scheduling_data = 2;   // List of schedules
  repeated DeviceScheduling device_scheduling_data = 3; // Device-specific steps
}

message Scheduling {
  int32 idx = 1;                           // Schedule index
  int32 start_time = 2;                    // Start time (minutes since midnight)
  int32 days_of_week = 3;                  // Days bitmask (0b01111111 = all days)
  int32 number_of_steps = 4;               // Total irrigation steps
  int32 waterpump_idx = 5;                 // Water pump device index
  int32 waterpump_working_time = 6;        // Pump operation time (minutes)
  int32 allow_ferti = 7;                   // Fertigation enabled (1=yes, 0=no)
  int32 ferti_idx = 8;                     // Fertigation device index
  int32 allow_backwash = 9;                // Backwash enabled (1=yes, 0=no)
  int32 backwash_idx = 10;                 // Backwash device index
  int32 group = 11;                        // Device group assignment
}

enum MsgType {
  MSG_NONE = 0;                            // No specific type
  MSG_SCHEDULING_ONLY = 1;                 // Only schedule data
  MSG_DEV_SCHEDULING_ONLY = 2;             // Only device scheduling data
  MSG_SCHEDULING_ALL = 3;                  // Both schedule and device data
}
```

**Field Descriptions**:

- `start_time`: Schedule start time in minutes from midnight (e.g., 360 = 6:00 AM)
- `days_of_week`: Bitmask where bit 0=Sunday, bit 1=Monday, etc.
- `number_of_steps`: Count of irrigation steps in the schedule
- `allow_ferti/allow_backwash`: Feature enable flags for optional operations

#### 4. DeviceSchedulingPackage - Device-Level Scheduling

**Purpose**: Define detailed device-specific scheduling steps with precise timing.

```protobuf
message DeviceSchedulingPackage {
  repeated DeviceScheduling data = 1;       // List of device scheduling steps
}

message DeviceScheduling {
  int32 idx = 1;                           // Device scheduling index
  int32 scheduling_idx = 2;                // Parent schedule reference
  int32 device_idx = 3;                    // Target device index
  int32 order = 4;                         // Execution order
  int32 sector_working_time = 5;           // Sector operation time (minutes)
  int32 ferti_working_time = 6;            // Fertigation time (minutes)
  int32 ferti_delay = 7;                   // Fertigation start delay (minutes)
}
```

**Field Descriptions**:

- `order`: Determines sequence of device activation within schedule
- `sector_working_time`: Duration for main irrigation operation
- `ferti_working_time`: Duration for fertigation if enabled
- `ferti_delay`: Time to wait before starting fertigation

#### 5. AutomationPackage - Automated Control Rules

**Purpose**: Configure automated responses based on sensor inputs.

```protobuf
message AutomationPackage {
  repeated AutomationData data = 1;         // List of automation rules
}

message AutomationData {
  int32 level_idx = 1;                     // Level sensor device index
  int32 pump_idx = 2;                      // Pump device index
  int32 mask = 3;                          // Condition evaluation mask
  int32 value = 4;                         // Trigger threshold value
  int32 working_time = 5;                  // Pump operation duration (minutes)
}
```

**Field Descriptions**:

- `level_idx`: Index of water level sensor device
- `pump_idx`: Index of pump to activate when conditions are met
- `mask`: Bitmask defining trigger conditions (e.g., low level = 6)
- `value`: Threshold value for sensor comparison
- `working_time`: Duration to run pump when triggered

#### 6. ControlPackage - Real-Time Device Control

**Purpose**: Send immediate control commands to specific devices.

```protobuf
message ControlPackage {
  int32 idx = 1;                           // Target device index
  MsgAction action = 2;                    // Control action to perform
  int32 working_time = 3;                  // Operation duration (minutes)
}

enum MsgAction {
  MSG_NONE = 0;                            // No action
  MSG_TURN_ON = 1;                         // Turn device on
  MSG_TURN_OFF = 2;                        // Turn device off
}
```

**Field Descriptions**:

- `idx`: Index of device to control
- `action`: Immediate action to perform (on/off)
- `working_time`: Duration for timed operations

#### 7. PauseSchedulingPackage - Schedule Pause Control

**Purpose**: Pause or resume irrigation scheduling system-wide.

```protobuf
message PauseSchedulingPackage {
  int32 state = 1;                         // Pause state (1=pause, 0=resume)
  int32 duration = 2;                      // Pause duration (minutes, 0=indefinite)
}
```

**Field Descriptions**:

- `state`: 1 to pause scheduling, 0 to resume
- `duration`: Minutes to pause (0 for indefinite pause)

#### 8. RequestInfoPackage - Information Request

**Purpose**: Request specific information from LIC device.

```protobuf
message RequestInfoPackage {
  int32 type = 1;                          // Information type requested
}
```

**Information Types**:

- `0`: System status information
- `1`: Device status information
- `2`: Scheduling status information
- `3`: Automation status information

#### 9. FirmwareUpdatePackage - Firmware Update Control

**Purpose**: Initiate firmware update process on LIC device.

```protobuf
message FirmwareUpdatePackage {
  MsgType type = 1;                        // Update target component
  MsgProtocol protocol = 2;                // Download protocol
  int32 activation_code = 3;               // Unique activation code
  int32 version = 4;                       // Target firmware version
}

enum MsgType {
  MSG_ESP = 0;                             // ESP32 firmware update
  MSG_STM = 1;                             // STM32 firmware update
}

enum MsgProtocol {
  MSG_HTTPS = 0;                           // HTTPS download
  MSG_HTTP = 1;                            // HTTP download
}
```

**Field Descriptions**:

- `type`: Target microcontroller for update
- `protocol`: Network protocol for firmware download
- `activation_code`: Security code to authorize update
- `version`: Target firmware version number

### Uplink Messages (OutgoingPacket Container)

All uplink messages are wrapped in the `OutgoingPacket` container:

```protobuf
message OutgoingPacket {
  uint64 id = 1;                                    // Message timestamp/ID
  oneof payload {
    codec.out.info.InfoPackage info = 2;
    codec.out.status.SystemStatusPackage status = 3;
    codec.out.scheduling_report.SchedulingReportPackage scheduling_report = 4;
    codec.out.automation_report.AutomationReportPackage automation_report = 5;
    codec.out.ack.AckPackage ack = 6;
  }
}
```

#### 1. InfoPackage - Device Information

**Purpose**: Report device identification, firmware versions, and synchronization status.

```protobuf
message InfoPackage {
  string codec_id = 1;                     // Device serial number
  uint32 firmware_esp = 2;                 // ESP32 firmware version
  uint32 firmware_mesh = 3;                // Mesh firmware version
  uint32 hardware_version = 4;             // Hardware version
  uint32 resets = 5;                       // Number of system resets
  uint32 scheduling_running = 6;           // Active scheduling count
  uint32 scheduling_paused = 7;            // Scheduling pause state
  uint32 devices_id = 8;                   // Last device configuration ID
  uint32 scheduling_id = 9;                // Last scheduling configuration ID
  uint32 dev_scheduling_id = 10;           // Last device scheduling ID
  uint32 automation_id = 11;               // Last automation configuration ID
  uint32 config_id = 12;                   // Last general configuration ID
}
```

**Field Descriptions**:

- `codec_id`: Unique device identifier matching MQTT topic
- `firmware_esp/firmware_mesh`: Version numbers for firmware components
- `resets`: System stability indicator
- `*_id` fields: Configuration version tracking for synchronization

#### 2. SystemStatusPackage - Operational Status

**Purpose**: Report current system operational state and sensor readings.

```protobuf
message SystemStatusPackage {
  uint32 resets = 1;                       // Number of system resets
  uint32 scheduling_running = 2;           // Active scheduling count
  uint32 scheduling_paused = 3;            // Scheduling pause state
  uint32 paused_time = 4;                  // Minutes since pause activated
  uint32 raining = 5;                      // Rain detection status (1=raining)
  uint32 rainfall = 6;                     // 24-hour rainfall accumulation (mm)
  uint64 sync_bitmask = 7;                 // Synchronized devices bitmask
  uint64 on_bitmask = 8;                   // Active devices bitmask
  uint64 input_bitmask = 9;                // Device input states bitmask
  uint32 failed_bitmask = 12;              // System failure bitmask
}
```

**Field Descriptions**:

- `scheduling_running`: Number of currently executing schedules
- `scheduling_paused`: 1 if scheduling is paused, 0 if active
- `raining`: Current rain detection status
- `rainfall`: Accumulated rainfall in last 24 hours
- `sync_bitmask`: Bitmask showing which devices are synchronized
- `on_bitmask`: Bitmask showing which devices are currently active
- `input_bitmask`: Bitmask showing device input states
- `failed_bitmask`: Bitmask indicating system failures

#### 3. SchedulingReportPackage - Scheduling Execution Report

**Purpose**: Report irrigation schedule execution status and results.

```protobuf
message SchedulingReportPackage {
  repeated SchedulingReportData data = 1;   // List of scheduling reports
}

message SchedulingReportData {
  int32 scheduling_idx = 1;                // Schedule index
  uint64 start_time = 2;                   // Schedule start timestamp
  uint64 end_time = 3;                     // Schedule end timestamp
  uint64 sector_bitmask1 = 4;              // Sectors activated (bits 0-63)
  uint64 sector_bitmask2 = 5;              // Sectors activated (bits 64-127)
  uint64 ferti_bitmask1 = 6;               // Fertigation sectors (bits 0-63)
  uint64 ferti_bitmask2 = 7;               // Fertigation sectors (bits 64-127)
  uint32 waterpump = 8;                    // Water pump status
  uint32 backwash = 9;                     // Backwash status
  uint64 backwash_time = 10;               // Backwash start timestamp
  int32 status = 11;                       // Schedule execution status
}
```

**Field Descriptions**:

- `scheduling_idx`: Reference to executed schedule
- `start_time/end_time`: Execution time window (Unix timestamps)
- `sector_bitmask1/2`: 128-bit bitmask showing which sectors were activated
- `ferti_bitmask1/2`: 128-bit bitmask showing which sectors received fertigation
- `waterpump`: Water pump operational status during schedule
- `backwash`: Backwash system status
- `status`: Overall execution status code

#### 4. AutomationReportPackage - Automation Execution Report

**Purpose**: Report automated control actions triggered by sensor inputs.

```protobuf
message AutomationReportPackage {
  repeated AutomationReportData data = 1;   // List of automation reports
}

message AutomationReportData {
  int32 auto_idx = 1;                      // Automation rule index
  uint64 start_time = 2;                   // Automation start timestamp
  uint64 restart_time = 3;                 // Restart timestamp (0 if no restart)
  uint64 end_time = 4;                     // End timestamp (0 if still running)
  int32 status = 5;                        // Execution status
}
```

**Field Descriptions**:

- `auto_idx`: Reference to automation rule that triggered
- `start_time`: When automation was first triggered
- `restart_time`: When automation was restarted (if applicable)
- `end_time`: When automation completed (0 if still active)
- `status`: Execution status code

#### 5. AckPackage - Acknowledgment

**Purpose**: Acknowledge receipt and processing of downlink messages.

```protobuf
message AckPackage {
  uint32 package = 1;                      // Acknowledged message type
  uint32 value = 2;                        // Acknowledgment value/status
}
```

**Field Descriptions**:

- `package`: Type of message being acknowledged
- `value`: Acknowledgment status or result code

## Database-Protobuf Relationship Mapping

### Downlink Message Construction (Database → Protobuf)

#### ConfigPackage Construction

**Source Tables**: Application SharedPreferences and system configuration
**Database Fields → Protobuf Fields**:

```kotlin
// From SharedPreferences and system settings
ConfigPackage {
  backwash_cycle = sharedPref.getInt("backwashCycle", 10)
  backwash_duration = sharedPref.getInt("backwashDuration", 5)
  backwash_delay = sharedPref.getInt("backwashDelay", 2)
  raingauge_enabled = sharedPref.getBoolean("rainGaugeEnabled", false)
  raingauge_factor = sharedPref.getInt("rainGaugeFactor", 1)
  rainfall_limit = sharedPref.getInt("rainfallLimit", 5)
  rainfall_pause_duration = sharedPref.getInt("rainfallPauseDuration", 60)
  wifi {
    ssid = codec.wifi_ssid
    password = codec.wifi_passwd
  }
  mesh {
    key = meshEncryptionKey
    channel = meshChannel
  }
}
```

#### DevicesPackage Construction

**Source Tables**: `Devices`, `Mesh_Devices`, `Groups`
**SQL Query**:

```sql
SELECT
  d.ord_idx AS ix,
  d.mesh_idx AS mi,
  d.identity AS di,
  d.type AS tp,
  d.out1 AS o1,
  d.out2 AS o2,
  d.input AS ip,
  d.mode AS md,
  d.sector AS sc,
  m.group_idx AS gp,
  d.eqpt_ver AS eq
FROM Devices d
JOIN Mesh_Devices m ON d.mesh_idx = m.idx
WHERE m.codec_idx = ?
ORDER BY d.ord_idx
```

**Database Fields → Protobuf Fields**:

```kotlin
DevicesData {
  idx = row["ix"]                    // d.ord_idx
  mesh_id = row["mi"]                // d.mesh_idx
  device_id = row["di"]              // d.identity
  device_type = row["tp"]            // d.type
  out1 = row["o1"]                   // d.out1
  out2 = row["o2"]                   // d.out2
  input = row["ip"]                  // d.input
  mode = row["md"]                   // d.mode
  sector = row["sc"]                 // d.sector
  group_idx = row["gp"]              // m.group_idx
  eqpt_ver = row["eq"]               // d.eqpt_ver
}
```

#### SchedulingPackage Construction

**Source Tables**: `Schedulings`, `Groups`
**SQL Query**:

```sql
SELECT
  s.ord_idx AS si,
  s.start_time AS st,
  s.days_of_week AS dw,
  s.number_of_steps AS ns,
  s.waterpump_ord_idx AS wi,
  s.waterpump_working_time AS wt,
  s.allow_ferti AS af,
  s.ferti_ord_idx AS fi,
  s.allow_backwash AS ab,
  s.backwash_ord_idx AS bi,
  s.group_idx AS gi
FROM Schedulings s
JOIN Groups g ON s.group_idx = g.idx
WHERE g.codec_idx = ?
ORDER BY s.ord_idx
```

**Database Fields → Protobuf Fields**:

```kotlin
Scheduling {
  idx = row["si"]                    // s.ord_idx
  start_time = row["st"]             // s.start_time
  days_of_week = row["dw"]           // s.days_of_week
  number_of_steps = row["ns"]        // s.number_of_steps
  waterpump_idx = row["wi"]          // s.waterpump_ord_idx
  waterpump_working_time = row["wt"] // s.waterpump_working_time
  allow_ferti = row["af"]            // s.allow_ferti
  ferti_idx = row["fi"]              // s.ferti_ord_idx
  allow_backwash = row["ab"]         // s.allow_backwash
  backwash_idx = row["bi"]           // s.backwash_ord_idx
  group = row["gi"]                  // s.group_idx
}
```

#### DeviceSchedulingPackage Construction

**Source Tables**: `Device_Schedulings`, `Schedulings`
**SQL Query**:

```sql
SELECT
  ds.ord_idx AS di,
  ds.scheduling_idx AS si,
  ds.device_idx AS dx,
  ds.n_order AS no,
  ds.sector_working_time AS st,
  ds.ferti_working_time AS ft,
  ds.ferti_delay AS fd
FROM Device_Schedulings ds
JOIN Schedulings s ON ds.scheduling_idx = s.idx
JOIN Groups g ON s.group_idx = g.idx
WHERE g.codec_idx = ?
ORDER BY ds.ord_idx
```

**Database Fields → Protobuf Fields**:

```kotlin
DeviceScheduling {
  idx = row["di"]                    // ds.ord_idx
  scheduling_idx = row["si"]         // ds.scheduling_idx
  device_idx = row["dx"]             // ds.device_idx
  order = row["no"]                  // ds.n_order
  sector_working_time = row["st"]    // ds.sector_working_time
  ferti_working_time = row["ft"]     // ds.ferti_working_time
  ferti_delay = row["fd"]            // ds.ferti_delay
}
```

#### AutomationPackage Construction

**Source Tables**: `Mesh_Devices`
**SQL Query**:

```sql
SELECT
  level_pump_idx AS li,
  ord_idx AS pi,
  6 AS mk,                          -- Fixed mask value for level automation
  0 AS vl,                          -- Fixed trigger value
  level_pump_working_time AS wt
FROM Mesh_Devices
WHERE codec_idx = ?
  AND level_pump_enable = 1
  AND level_pump_idx IS NOT NULL
```

**Database Fields → Protobuf Fields**:

```kotlin
AutomationData {
  level_idx = row["li"]              // level_pump_idx
  pump_idx = row["pi"]               // ord_idx (pump device index)
  mask = row["mk"]                   // Fixed value: 6 (level trigger)
  value = row["vl"]                  // Fixed value: 0 (threshold)
  working_time = row["wt"]           // level_pump_working_time
}
```

### Uplink Message Processing (Protobuf → Database)

#### InfoPackage Processing

**Target Tables**: `Codecs` table timestamp updates
**Protobuf Fields → Database Updates**:

```kotlin
// Update codec synchronization timestamps
UPDATE Codecs SET
  last_devices_update = info.devices_id,
  last_scheduling_update = info.scheduling_id,
  last_device_scheduling_update = info.dev_scheduling_id,
  last_automation_update = info.automation_id,
  last_config_update = info.config_id
WHERE identity = info.codec_id
```

#### SystemStatusPackage Processing

**Target Tables**: In-memory state management (codecUpdateOnMap)
**Protobuf Fields → Application State**:

```kotlin
LatestCodecUpdates {
  codecFirmware = status.firmware_esp
  pauseScheduling = (status.scheduling_paused == 1)
  schedulingRunning = (status.scheduling_running > 0)
  syncBitmask = status.sync_bitmask
  onBitmask = status.on_bitmask
  inputBitmask = status.input_bitmask
  failedBitmask = status.failed_bitmask
  rainStatus = (status.raining == 1)
  rainfall = status.rainfall
  updated = true
}
```

#### Report Message Processing

**Target Tables**: Message storage in ConcurrentHashMap
**Processing Logic**:

```kotlin
// Store scheduling reports for later analysis
messageList[codecIdentity to SCHEDULING_REPORT] = packet.toByteArray()

// Store automation reports for later analysis
messageList[codecIdentity to AUTOMATION_REPORT] = packet.toByteArray()

// Persist message list to SharedPreferences
saveMessageList()
```

## Data Construction and Source Documentation

### Message Construction Requirements

#### ConfigPackage Data Sources

**Required Data**:

- **SharedPreferences**: `backwashCycle`, `backwashDuration`, `backwashDelay`, `rainGaugeEnabled`, `rainGaugeFactor`, `rainfallLimit`, `rainfallPauseDuration`
- **Codecs Table**: `wifi_ssid`, `wifi_passwd` from target codec record
- **System Constants**: Mesh encryption key and channel (hardcoded or configuration)

**Construction SQL**: N/A (uses SharedPreferences and constants)

**Validation Rules**:

- WiFi SSID and password must not be null for device connectivity
- Rain gauge factor must be positive integer
- Rainfall limit must be non-negative
- Duration values must be positive integers

#### DevicesPackage Data Sources

**Required Tables**: `Devices`, `Mesh_Devices`, `Groups`
**Construction SQL**:

```sql
SELECT
  d.ord_idx, d.mesh_idx, d.identity, d.type,
  d.out1, d.out2, d.input, d.mode, d.sector,
  m.group_idx, d.eqpt_ver
FROM Devices d
JOIN Mesh_Devices m ON d.mesh_idx = m.idx
JOIN Groups g ON m.group_idx = g.idx
WHERE g.codec_idx = ?
ORDER BY d.ord_idx
```

**Validation Rules**:

- Device identity must be unique within mesh device
- Device type must be valid integer code
- Sector assignment must be positive integer or null
- ord_idx determines message order and must be sequential

**Default Values**:

- `out1`, `out2`, `input`, `mode`: Default to 0 if null
- `sector`: Default to 0 if null
- `eqpt_ver`: Default to 0 if null

#### SchedulingPackage Data Sources

**Required Tables**: `Schedulings`, `Groups`
**Construction SQL**:

```sql
SELECT
  s.ord_idx, s.start_time, s.days_of_week, s.number_of_steps,
  s.waterpump_ord_idx, s.waterpump_working_time,
  s.allow_ferti, s.ferti_ord_idx,
  s.allow_backwash, s.backwash_ord_idx,
  s.group_idx
FROM Schedulings s
JOIN Groups g ON s.group_idx = g.idx
WHERE g.codec_idx = ? AND s.enabled = 1
ORDER BY s.ord_idx
```

**Validation Rules**:

- `start_time` must be 0-1439 (minutes in day)
- `days_of_week` must be valid bitmask (0-127)
- `number_of_steps` must match actual device scheduling count
- Optional pump indices must reference valid devices

**Business Logic**:

- Only enabled schedules are included in message
- Pump working times default to 0 if not specified
- Feature flags (ferti, backwash) control optional operations

#### DeviceSchedulingPackage Data Sources

**Required Tables**: `Device_Schedulings`, `Schedulings`, `Groups`
**Construction SQL**:

```sql
SELECT
  ds.ord_idx, ds.scheduling_idx, ds.device_idx, ds.n_order,
  ds.sector_working_time, ds.ferti_working_time, ds.ferti_delay
FROM Device_Schedulings ds
JOIN Schedulings s ON ds.scheduling_idx = s.idx
JOIN Groups g ON s.group_idx = g.idx
WHERE g.codec_idx = ?
ORDER BY ds.ord_idx
```

**Validation Rules**:

- `n_order` must be sequential within each scheduling
- `sector_working_time` must be positive integer
- `ferti_working_time` can be null (defaults to 0)
- `ferti_delay` must be non-negative

**Business Logic**:

- Device scheduling steps define precise irrigation timing
- Order determines sequence of device activation
- Fertigation timing is optional and controlled per step

#### AutomationPackage Data Sources

**Required Tables**: `Mesh_Devices`
**Construction SQL**:

```sql
SELECT
  level_pump_idx, ord_idx, level_pump_working_time
FROM Mesh_Devices
WHERE codec_idx = ?
  AND level_pump_enable = 1
  AND level_pump_idx IS NOT NULL
  AND level_pump_working_time > 0
```

**Validation Rules**:

- `level_pump_idx` must reference valid device
- `level_pump_working_time` must be positive
- Level pump automation must be explicitly enabled

**Business Logic**:

- Automation rules are generated from mesh device level pump configuration
- Fixed mask value (6) represents low water level trigger
- Fixed threshold value (0) for binary level detection

### Message Processing Requirements

#### Change Detection Logic

**Timestamp Comparison**:

```kotlin
// Check if update needed based on timestamps
if (codec["last_devices_update"] > codecUpdateOnMap[identity]?.devicesId) {
    // Send DevicesPackage
}
if (codec["last_scheduling_update"] > codecUpdateOnMap[identity]?.schedulingId) {
    // Send SchedulingPackage
}
// Similar checks for other message types
```

**Update Triggers**:

- Database modifications update corresponding timestamp in Codecs table
- Timestamp comparison determines which messages need transmission
- Prevents unnecessary MQTT traffic for unchanged configurations

#### Message Serialization Process

**Protobuf Serialization**:

```kotlin
val packet = IncomingPacketOuterClass.IncomingPacket.newBuilder()
    .setId(timestamp)
    .setDevices(devicePackage)
    .build()

val payload = packet.toByteArray()
val crc = Utils.crc16(payload)
val crcBytes = byteArrayOf(
    ((crc ushr 8) and 0xFF).toByte(),
    (crc and 0xFF).toByte()
)
val finalPayload = payload + crcBytes
```

**Quality Assurance**:

- CRC16 checksum appended to all messages
- Message ID uses timestamp for tracking and deduplication
- Binary payload transmitted via MQTT with QoS level 0

#### Error Handling and Recovery

**Database Transaction Management**:

- Batch operations use database transactions
- Rollback on any insertion failure
- Foreign key constraints prevent orphaned records

**MQTT Communication Resilience**:

- Automatic reconnection on connection loss
- Message queuing during disconnection periods
- Retry logic for failed transmissions

**Data Validation**:

- Input validation before database insertion
- Protobuf field validation before serialization
- Range checking for numeric values
- Null handling for optional fields
