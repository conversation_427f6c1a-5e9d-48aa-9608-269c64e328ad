# IrrigaNet - Technical Documentation

## Overview

IrrigaNet is an Android mobile application designed for configuring and monitoring Localized Irrigation Controllers (LIC) in agricultural irrigation systems. The application serves as a simplified configuration tool that communicates with irrigation hardware via MQTT protocol using Protocol Buffers (protobuf) for message serialization.

## Structure & Architecture

### Directory Structure

```
irriganet/
├── app/                           # Main Android application
│   ├── build.gradle              # Android build configuration
│   ├── proguard-rules.pro        # ProGuard configuration
│   └── src/main/
│       ├── AndroidManifest.xml   # App manifest
│       ├── java/br/com/byagro/irriganet/  # Kotlin source code
│       ├── proto/                 # Protocol Buffer definitions
│       └── res/                   # Android resources
├── build.gradle                   # Project-level build configuration
├── gradle/                        # Gradle wrapper and dependencies
├── gradle.properties             # Gradle properties
├── gradlew                       # Gradle wrapper script (Unix)
├── gradlew.bat                   # Gradle wrapper script (Windows)
└── settings.gradle               # Gradle settings
```

### Module Hierarchy

The application follows Android's standard architecture with these key components:

- **MainActivity.kt**: Central coordinator managing MQTT communication and navigation
- **UI Fragments**: Modular screens for different functionalities
- **DBHelper.kt**: SQLite database management layer
- **MqttManager.kt**: MQTT client wrapper using HiveMQ library
- **WebService.kt**: HTTP API communication utilities
- **SharedData.kt**: Global state management

### Entry Points

- **Main Entry**: `MainActivity` - Launched activity with navigation drawer
- **Navigation**: Fragment-based navigation using Android Navigation Component
- **Default Screen**: Report fragment (`nav_report`) showing system overview

## Technical Implementation

### Programming Languages & Frameworks

- **Primary Language**: Kotlin for Android
- **UI Framework**: Android Jetpack with Navigation Component
- **Database**: SQLite with custom ORM (DBHelper)
- **MQTT Client**: HiveMQ MQTT Client v1.3.7
- **Serialization**: Google Protocol Buffers v3.25.3
- **Async Processing**: Kotlin Coroutines
- **HTTP Client**: Android Volley v1.2.1

### Key Dependencies

```gradle
// Core Android
implementation libs.androidx.core.ktx
implementation libs.androidx.appcompat
implementation libs.material
implementation libs.androidx.constraintlayout

// Navigation
implementation libs.androidx.navigation.fragment.ktx
implementation libs.androidx.navigation.ui.ktx

// MQTT Communication
implementation("com.hivemq:hivemq-mqtt-client:1.3.7")

// Protocol Buffers
implementation 'com.google.protobuf:protobuf-kotlin-lite:3.25.3'

// JSON Processing
implementation 'com.google.code.gson:gson:2.8.9'

// HTTP Client
implementation 'com.android.volley:volley:1.2.1'

// Coroutines
implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.8.0")
implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.0")

// CBOR Serialization
implementation("com.upokecenter:cbor:4.5.2")
```

### Build System Configuration

- **Build Tool**: Gradle with Android Gradle Plugin
- **Compile SDK**: 35 (Android 14)
- **Target SDK**: 35
- **Minimum SDK**: 24 (Android 7.0)
- **Java Version**: 11
- **Kotlin JVM Target**: 11
- **Protobuf Plugin**: v0.9.5 for automatic code generation

### Database Schema

The application uses SQLite with 7 main tables:

1. **Codecs**: LIC device registry with WiFi credentials
2. **Groups**: Logical groupings of irrigation devices
3. **Mesh_Devices**: Mesh network device definitions
4. **Devices**: Individual device endpoints with operational parameters
5. **Schedulings**: Irrigation schedule definitions
6. **Device_Schedulings**: Device-specific scheduling steps
7. **Sector_Schedulings**: Sector-based scheduling configurations

## Communication & Protocols

### MQTT Communication Architecture

**Broker Configuration**:
- **Host**: `mosquitto-codec.saas.byagro.dev.br`
- **Port**: 8003
- **Protocol**: MQTT v3.1.1 and v5.0 support
- **Authentication**: Username/password (`codec` / base64 encoded)
- **Client ID**: `IrrigaNet-{UUID}` for unique identification

**Topic Structure**:
```
/codec/{device_identifier}/report    # Uplink messages (FROM device)
/codec/{device_identifier}/downlink  # Downlink messages (TO device)
```

### Protocol Buffer Message Definitions

#### Incoming Messages (TO LIC Devices)

**Container Message**:
```protobuf
message IncomingPacket {
  uint64 id = 1;
  oneof payload {
    codec.in.config.ConfigPackage config = 2;
    codec.in.devices.DevicesPackage devices = 3;
    codec.in.scheduling.SchedulingPackage scheduling = 4;
    codec.in.device_scheduling.DeviceSchedulingPackage dev_scheduling = 5;
    codec.in.automation.AutomationPackage automation = 6;
    codec.in.control.ControlPackage control = 7;
    codec.in.pause.PauseSchedulingPackage pause = 8;
    codec.in.request_info.RequestInfoPackage request_info = 9;
    codec.in.firmware_update.FirmwareUpdatePackage firmware_update = 10;
  }
}
```

#### Outgoing Messages (FROM LIC Devices)

**Container Message**:
```protobuf
message OutgoingPacket {
  uint64 id = 1;
  oneof payload {
    codec.out.info.InfoPackage info = 2;
    codec.out.status.SystemStatusPackage status = 3;
    codec.out.scheduling_report.SchedulingReportPackage scheduling_report = 4;
    codec.out.automation_report.AutomationReportPackage automation_report = 5;
    codec.out.ack.AckPackage ack = 6;
  }
}
```

### Message Exchange Mechanisms

1. **Configuration Updates**: App sends device/scheduling configurations
2. **Status Monitoring**: Devices report operational status and sensor data
3. **Control Commands**: Real-time device control (start/stop irrigation)
4. **Firmware Updates**: OTA firmware update coordination
5. **Acknowledgments**: Confirmation of received and processed messages

## User Interaction

### User Interface Architecture

**Navigation Pattern**: Material Design Navigation Drawer with Fragment-based screens

**Main Screens**:
1. **Report (Visão Geral)**: System overview and status dashboard
2. **Irrigation (Irrigação)**: Irrigation scheduling and group management
3. **Individual Pumps (Bombas)**: Service pump control interface
4. **Configuration**: System settings and device management

### User Workflows

#### Device Registration Workflow
1. Navigate to Codec Registration (`nav_codec_reg`)
2. Enter device identity, name, and WiFi credentials
3. System stores codec information and establishes MQTT subscription
4. Device appears in system overview

#### Irrigation Configuration Workflow
1. Access Irrigation screen
2. Create/modify irrigation groups
3. Configure scheduling parameters
4. Deploy configuration to LIC devices via MQTT
5. Monitor execution status

#### Device Control Workflow
1. Select device from device manager
2. Access device control interface
3. Send real-time control commands (start/stop)
4. Monitor device response and status updates

### Authentication & Authorization

- **Local Authentication**: No centralized authentication system
- **Device Access**: Direct MQTT connection to configured devices
- **Security**: WiFi credentials stored locally in SQLite database
- **Network Security**: MQTT over TLS (implied by broker configuration)

### Configuration Options

**Application Settings**:
- Rain gauge configuration (enabled/disabled, sensitivity)
- Backwash cycle parameters
- WiFi network configuration for devices
- Mesh network encryption settings
- Rainfall limits and pause durations

## Data Flow & Processing

### Message Processing Pipeline

1. **MQTT Message Reception**: HiveMqttManager receives binary messages
2. **Protocol Buffer Deserialization**: Messages parsed using generated protobuf classes
3. **Database Updates**: Relevant data stored/updated in SQLite database
4. **UI Updates**: LiveData/ViewModel pattern updates UI components
5. **User Feedback**: Status indicators and notifications

### Message Serialization/Deserialization

- **Outbound**: Kotlin objects → Protobuf binary → MQTT publish
- **Inbound**: MQTT message → Protobuf binary → Kotlin objects → Database/UI
- **Error Handling**: CRC validation and message integrity checks
- **Logging**: Comprehensive MQTT message logging with hex dump

### Error Handling & Logging

**Error Categories**:
- MQTT connection failures with automatic reconnection
- Protobuf parsing errors with graceful degradation
- Database operation failures with transaction rollback
- Network timeouts with retry mechanisms

**Logging Strategy**:
- MQTT message traffic logging with hex representation
- Database operation logging
- UI interaction logging
- Error stack trace capture

### Performance Considerations

- **Background Processing**: MQTT handling on IO dispatcher
- **Database Optimization**: Indexed queries and prepared statements
- **Memory Management**: Efficient protobuf object lifecycle
- **Battery Optimization**: Optimized MQTT keepalive intervals

## Integration Points

### External System Integrations

**MQTT Broker Integration**:
- Mosquitto broker hosted at `mosquitto-codec.saas.byagro.dev.br`
- Supports multiple concurrent client connections
- Message persistence and QoS level management

**Web Service Integration**:
- HTTP API endpoints for additional data synchronization
- JSON-based communication using Volley HTTP client
- Base64 encoding for binary data transmission

### Hardware Interface Patterns

**LIC Device Communication**:
- ESP32-based irrigation controllers
- Mesh networking capability for extended range
- Multiple I/O channels for sensors and actuators
- Firmware update capability via MQTT

**Sensor Integration**:
- Rain gauge sensors with configurable sensitivity
- Soil moisture sensors for automation triggers
- Flow sensors for irrigation monitoring
- Level sensors for reservoir management

### Database Integration

**Local Data Persistence**:
- SQLite database with foreign key constraints
- Automatic schema migration support
- Concurrent access handling with proper locking
- Data export/import capabilities for backup

**Data Synchronization**:
- Timestamp-based change detection
- Incremental updates to minimize bandwidth
- Conflict resolution for concurrent modifications
- Offline operation support with sync on reconnection

## Related System Components

### MQTT Integration Service

The broader system includes a Node.js/TypeScript MQTT integration service (`mqtt-integration/`) that:
- Processes MQTT messages from multiple LIC devices
- Integrates with the main Directus-based backend system
- Handles message queuing and reliable delivery
- Provides comprehensive logging and monitoring

### Protobuf Definitions

Shared protobuf definitions (`protobuf/`) used across:
- Android application (this component)
- MQTT integration service
- Backend API systems
- Hardware firmware implementations

### Database Integration

The Android app operates independently but shares conceptual models with the main system's PostgreSQL database managed through Directus CMS, enabling future integration and data synchronization capabilities.
