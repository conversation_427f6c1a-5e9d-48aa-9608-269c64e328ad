import type { <PERSON><PERSON>Model } from "@/api/model/device";
import { MODEL_LABELS } from "@/utils/device-model";
import type { AUTDevice } from "@/api/queries/account";
import { Modal, useToast } from "@/components";
import Button from "@/components/ui/Button";
import { ModalFooter } from "@/components/ui/ModalFooter";
import {
  createPropertyDeviceAtom,
  selectedPropertyIdAtom,
  updatePropertyDeviceAtom,
} from "@/store";
import { useAtomValue, useSetAtom } from "jotai";
import { Eye, EyeOff, Link, AlertTriangle } from "lucide-react";
import { useEffect, useState } from "react";
import type { DeviceWithMapping } from "@/utils/mesh-device-utils";
import {
  getDeviceIcon,
  formatMappingDate,
  isMeshDevice,
} from "@/utils/mesh-device-utils";
import { getDeviceModelLabel } from "@/utils/device-model";

interface DeviceDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  device: AUTDevice | null;
  enhancedDevice?: DeviceWithMapping | null; // Optional enhanced device info for mesh mapping display
  mode: "create" | "edit";
  onOpenAssignToLIC?: (device: DeviceWithMapping) => void; // Callback to open assign to LIC modal
}

function DeviceDetailModal({
  isOpen,
  onClose,
  device,
  enhancedDevice,
  mode,
  onOpenAssignToLIC,
}: DeviceDetailModalProps) {
  const updatePropertyDevice = useSetAtom(updatePropertyDeviceAtom);
  const createPropertyDevice = useSetAtom(createPropertyDeviceAtom);
  const selectedPropertyId = useAtomValue(selectedPropertyIdAtom);
  const { showWarning } = useToast();

  const [formData, setFormData] = useState({
    identifier: "",
    model: "LIC" as DeviceModel,
    label: "",
    wifiSSID: "",
    wifiPassword: "",
  });
  const [showWifiPassword, setShowWifiPassword] = useState(false);
  const [identifierError, setIdentifierError] = useState<string | null>(null);

  // Validation functions
  const validateIdentifier = (
    identifier: string,
    model: DeviceModel
  ): string | null => {
    if (!identifier.trim()) {
      return "Identificador é obrigatório";
    }

    const hexPattern = /^[0-9A-F]+$/;
    if (!hexPattern.test(identifier)) {
      return "Identificador deve conter apenas caracteres hexadecimais (0-9, A-F)";
    }

    const expectedLength = model === "LIC" ? 12 : 6;
    if (identifier.length !== expectedLength) {
      return `Identificador deve ter ${expectedLength} caracteres para dispositivos ${model}`;
    }

    return null;
  };

  const handleIdentifierChange = (value: string) => {
    // Automatically capitalize and filter only hex characters
    const processedValue = value.toUpperCase().replace(/[^0-9A-F]/g, "");

    setFormData({ ...formData, identifier: processedValue });

    // Validate the identifier
    const error = validateIdentifier(processedValue, formData.model);
    setIdentifierError(error);
  };

  const handleModelChange = (model: DeviceModel) => {
    setFormData({ ...formData, model });

    // Re-validate identifier with new model
    if (formData.identifier) {
      const error = validateIdentifier(formData.identifier, model);
      setIdentifierError(error);
    }
  };

  useEffect(() => {
    if (device && mode === "edit") {
      // For LIC devices, get WIFI configuration from PropertyDevice metadata instead of Device metadata
      const metadata =
        device.model === "LIC" && enhancedDevice?.metadata
          ? (enhancedDevice.metadata as any)
          : (device.metadata as any);

      setFormData({
        identifier: device.identifier,
        model: device.model,
        label: metadata?.label || "",
        wifiSSID: metadata?.wifiSSID || "",
        wifiPassword: metadata?.wifiPassword || "",
      });
      setIdentifierError(null);
    } else if (mode === "create") {
      setFormData({
        identifier: "",
        model: "LIC" as DeviceModel,
        label: "",
        wifiSSID: "",
        wifiPassword: "",
      });
      setIdentifierError(null);
    }
  }, [device, mode, enhancedDevice]);

  const handleSave = async () => {
    if (!selectedPropertyId) {
      console.error("No property selected for the device.");
      showWarning({
        message: "Nenhuma propriedade selecionada para o dispositivo.",
      });
      return;
    }

    // Validate identifier before saving
    const identifierValidationError = validateIdentifier(
      formData.identifier,
      formData.model
    );
    if (identifierValidationError) {
      setIdentifierError(identifierValidationError);
      showWarning({
        message: "Por favor, corrija os erros no formulário antes de salvar.",
      });
      return;
    }

    // Prepare metadata for LIC devices
    const metadata =
      formData.model === "LIC"
        ? {
            wifiSSID: formData.wifiSSID || null,
            wifiPassword: formData.wifiPassword || null,
            label: formData.label || null,
          }
        : {
            label: formData.label || null,
          };

    try {
      if (mode === "create") {
        await createPropertyDevice({
          property: selectedPropertyId,
          device: {
            identifier: formData.identifier,
            model: formData.model,
          } as any, // Type assertion to bypass strict typing for nested creation
          metadata,
          start_date: new Date().toISOString(),
        });
      } else if (mode === "edit" && device && enhancedDevice) {
        await updatePropertyDevice({
          id: enhancedDevice.id, // PropertyDevice ID
          data: {
            device: {
              id: device.id, // Directus ID of the device
              identifier: formData.identifier,
              model: formData.model,
            } as any,
            metadata,
          },
        });
      }
      onClose();
    } catch (error) {
      console.error("Error saving device:", error);
      showWarning({
        message: "Erro ao salvar dispositivo. Tente novamente.",
      });
    }
  };

  const handleCancel = () => {
    onClose();
  };

  const getModelOptions = () =>
    Object.entries(MODEL_LABELS).map(([value, label]) => ({ value, label }));

  // Determine if model select should be disabled (LIC with mapped mesh devices)
  const isLICWithMappedDevices = (() => {
    if (
      mode === "edit" &&
      device &&
      device.model === "LIC" &&
      enhancedDevice &&
      Array.isArray(enhancedDevice.meshDevices) &&
      enhancedDevice.meshDevices.length > 0
    ) {
      return true;
    }
    return false;
  })();

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === "create" ? "Novo Dispositivo" : "Editar Dispositivo"}
      size="md"
    >
      <div className="space-y-6">
        {/* Serial Number */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            S/N
          </label>
          <input
            type="text"
            value={formData.identifier}
            onChange={(e) => handleIdentifierChange(e.target.value)}
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none ${
              identifierError ? "border-red-300" : "border-gray-300"
            }`}
            placeholder={formData.model === "LIC" ? "XXXXXXXXXXXX" : "XXXXXX"}
            maxLength={formData.model === "LIC" ? 12 : 6}
          />
          {identifierError && (
            <p className="mt-1 text-sm text-red-600">{identifierError}</p>
          )}
          <p className="mt-1 text-xs text-gray-500">
            {formData.model === "LIC"
              ? "12 caracteres hexadecimais (0-9, A-F)"
              : "6 caracteres hexadecimais (0-9, A-F)"}
          </p>
        </div>

        {/* Model */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Modelo
          </label>
          <select
            value={formData.model}
            onChange={(e) => handleModelChange(e.target.value as DeviceModel)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none disabled:bg-gray-100 disabled:text-gray-500"
            disabled={isLICWithMappedDevices}
            title={
              isLICWithMappedDevices
                ? "Não é possível alterar o modelo de um LIC que possui dispositivos mesh mapeados."
                : undefined
            }
          >
            {getModelOptions().map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          {isLICWithMappedDevices && (
            <p className="mt-1 text-xs text-yellow-700">
              Não é possível alterar o modelo de um LIC que possui dispositivos
              mesh mapeados.
            </p>
          )}
        </div>

        {/* Label */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Rótulo (Opcional)
          </label>
          <input
            type="text"
            value={formData.label}
            onChange={(e) =>
              setFormData({ ...formData, label: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none"
            placeholder="Nome personalizado para o dispositivo"
          />
          <p className="mt-1 text-xs text-gray-500">
            Rótulo personalizado para identificar facilmente este dispositivo
          </p>
        </div>

        {/* Wi-Fi Configuration - Only for LIC devices */}
        {formData.model === "LIC" && (
          <>
            {/* Wi-Fi SSID */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                SSID do Wi-Fi
              </label>
              <input
                type="text"
                value={formData.wifiSSID}
                onChange={(e) =>
                  setFormData({ ...formData, wifiSSID: e.target.value })
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none"
                placeholder="Nome da rede Wi-Fi"
              />
            </div>

            {/* Wi-Fi Password with visibility toggle */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Senha do Wi-Fi
              </label>
              <div className="relative">
                <input
                  type={showWifiPassword ? "text" : "password"}
                  value={formData.wifiPassword}
                  onChange={(e) =>
                    setFormData({ ...formData, wifiPassword: e.target.value })
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none pr-12"
                  placeholder="Senha da rede Wi-Fi"
                />
                <Button
                  type="button"
                  aria-label={
                    showWifiPassword ? "Ocultar senha" : "Mostrar senha"
                  }
                  onClick={() => setShowWifiPassword((v) => !v)}
                  variant="ghost"
                  className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none"
                  tabIndex={0}
                >
                  {showWifiPassword ? (
                    <EyeOff className="h-5 w-5" />
                  ) : (
                    <Eye className="h-5 w-5" />
                  )}
                </Button>
              </div>
            </div>
          </>
        )}

        {/* Mesh Device Mapping Information - Only for mesh devices in edit mode */}
        {mode === "edit" &&
          enhancedDevice &&
          isMeshDevice(enhancedDevice.device.model) && (
            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
                <span>{getDeviceIcon(enhancedDevice.device.model)}</span>
                Mapeamento de Rede Mesh
              </h3>

              {enhancedDevice.mappingStatus === "mapped" &&
              enhancedDevice.licDevice ? (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Link className="text-green-600" size={16} />
                      <span className="font-medium text-green-800">
                        Mapeado para LIC
                      </span>
                    </div>
                    {onOpenAssignToLIC && (
                      <Button
                        onClick={() => onOpenAssignToLIC(enhancedDevice)}
                        variant="primary"
                        size="sm"
                        className="px-3 py-1.5"
                      >
                        Alterar Mapeamento
                      </Button>
                    )}
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <span className="text-lg">📡</span>
                      <span className="font-medium">
                        {enhancedDevice.licDevice.device.identifier}
                      </span>
                      <span className="text-gray-600">
                        (
                        {getDeviceModelLabel(
                          enhancedDevice.licDevice.device.model
                        )}
                        )
                      </span>
                    </div>
                    {enhancedDevice.current_mesh_device_mapping && (
                      <div className="text-gray-600">
                        Mapeado desde:{" "}
                        {formatMappingDate(
                          enhancedDevice.current_mesh_device_mapping.start_date
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="text-yellow-600" size={16} />
                      <span className="font-medium text-yellow-800">
                        Não Mapeado
                      </span>
                    </div>
                    {onOpenAssignToLIC && (
                      <Button
                        onClick={() => onOpenAssignToLIC(enhancedDevice)}
                        variant="primary"
                        size="sm"
                        className="px-3 py-1.5"
                      >
                        Mapear
                      </Button>
                    )}
                  </div>
                  <p className="text-sm text-yellow-700">
                    Este dispositivo não está associado a nenhum LIC. Clique em
                    "Mapear" para associá-lo a um coordenador.
                  </p>
                </div>
              )}
            </div>
          )}

        {/* Action Buttons */}
        <ModalFooter
          primaryAction={{
            label: "Salvar",
            onClick: handleSave,
          }}
          secondaryAction={{
            label: "Cancelar",
            onClick: handleCancel,
          }}
        />
      </div>
    </Modal>
  );
}

export default DeviceDetailModal;
