import type { Reservoir } from "@/api/model/reservoir";
import {
  createReservoir<PERSON>tom,
  devicesAtom,
  reservoirsAtom,
  reservoirsByIdAtom,
  selectedPropertyIdAtom,
  updateReservoirAtom,
  waterPumpsAtom,
} from "@/store";
import { useAtomValue, useSetAtom } from "jotai";
import { useEffect, useState } from "react";
import { useLocation, useParams } from "wouter";
import Button from "@/components/ui/Button";
import { ModalFooter } from "@/components/ui/ModalFooter";

interface ReservoirFormData {
  name: string;
  description: string;
  capacity: string;
  reservoir_monitor: string;
  water_pump: string;
  enabled: boolean;
  notes: string;
}

export function ReservoirForm() {
  const [, setLocation] = useLocation();
  const { id } = useParams<{ id: string }>();
  const isEditing = id !== "new";

  const devices = useAtomValue(devicesAtom);
  const waterPumps = useAtomValue(waterPumpsAtom);
  const selectedPropertyId = useAtomValue(selectedPropertyIdAtom);
  const reservoirById = useAtomValue(reservoirsByIdAtom);
  const reservoirs = useAtomValue(reservoirsAtom);
  // IDs of RM devices already assigned to other reservoirs (excluding current when editing)
  const usedRMDeviceIds = reservoirs
    .filter((r: any) => !isEditing || r.id !== id)
    .map((r: any) =>
      r.reservoir_monitor && typeof r.reservoir_monitor === "object"
        ? r.reservoir_monitor.id
        : r.reservoir_monitor
    )
    .filter(Boolean) as string[];
  // IDs of water pumps already assigned to other reservoirs (excluding current when editing)
  const usedPumpIds = reservoirs
    .filter((r: any) => !isEditing || r.id !== id)
    .map((r: any) =>
      r.water_pump && typeof r.water_pump === "object"
        ? r.water_pump.id
        : r.water_pump
    )
    .filter(Boolean) as string[];
  const createReservoir = useSetAtom(createReservoirAtom);
  const updateReservoir = useSetAtom(updateReservoirAtom);

  const [formData, setFormData] = useState<ReservoirFormData>({
    name: "",
    description: "",
    capacity: "",
    reservoir_monitor: "",
    water_pump: "",
    enabled: true,
    notes: "",
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load existing reservoir data when editing or reset when creating
  useEffect(() => {
    if (isEditing && id) {
      const reservoir = reservoirById(id);
      if (reservoir) {
        setFormData({
          name: (reservoir as any).name || "",
          description: (reservoir as any).description || "",
          capacity: (reservoir as any).capacity?.toString() || "",
          reservoir_monitor: (reservoir as any).reservoir_monitor || "",
          water_pump: (reservoir as any).water_pump || "",
          enabled: (reservoir as any).enabled,
          notes: (reservoir as any).notes || "",
        });
      }
    } else if (!isEditing) {
      // Reset form data when creating new reservoir
      setFormData({
        name: "",
        description: "",
        capacity: "",
        reservoir_monitor: "",
        water_pump: "",
        enabled: true,
        notes: "",
      });
    }
  }, [isEditing, id, reservoirById]);

  // Filter devices to only show RM (Reservoir Monitor) devices
  const rmDevices = devices.filter((device: any) => device.model === "RM");

  // Filter water pumps to only show SERVICE type pumps
  const servicePumps = waterPumps.filter(
    (pump: any) => pump.pump_type === "SERVICE"
  );

  const handleInputChange =
    (field: keyof ReservoirFormData) =>
    (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      setFormData((prev) => ({
        ...prev,
        [field]: event.target.value,
      }));
    };

  const handleSelectChange =
    (field: keyof ReservoirFormData) =>
    (event: React.ChangeEvent<HTMLSelectElement>) => {
      setFormData((prev) => ({
        ...prev,
        [field]: event.target.value,
      }));
    };

  const handleCheckboxChange =
    (field: keyof ReservoirFormData) =>
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setFormData((prev) => ({
        ...prev,
        [field]: event.target.checked,
      }));
    };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const reservoirData: Partial<Reservoir> = {
        property: selectedPropertyId!,
        name: formData.name,
        description: formData.description || null,
        capacity: formData.capacity ? parseFloat(formData.capacity) : null,
        reservoir_monitor: formData.reservoir_monitor || null,
        water_pump: formData.water_pump || null,
        enabled: formData.enabled,
        notes: formData.notes || null,
      };

      if (isEditing && id) {
        await updateReservoir({ id, data: reservoirData });
      } else {
        await createReservoir(reservoirData);
      }

      setLocation("/app/reservoirs");
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Erro ao salvar reservatório"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setLocation("/app/reservoirs");
  };

  return (
    <div className="p-4 max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold text-gray-900 mb-6">
        {isEditing ? "Editar Reservatório" : "Novo Reservatório"}
      </h1>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      <form className="space-y-6">
        <div>
          <label
            htmlFor="name"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Nome *
          </label>
          <input
            type="text"
            id="name"
            value={formData.name}
            onChange={handleInputChange("name")}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
            placeholder="Nome do reservatório"
          />
        </div>

        <div>
          <label
            htmlFor="description"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Descrição
          </label>
          <textarea
            id="description"
            value={formData.description}
            onChange={handleInputChange("description")}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
            placeholder="Descrição do reservatório"
          />
        </div>

        <div>
          <label
            htmlFor="capacity"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Capacidade (litros)
          </label>
          <input
            type="number"
            id="capacity"
            value={formData.capacity}
            onChange={handleInputChange("capacity")}
            min="0"
            step="0.1"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
            placeholder="Capacidade em litros"
          />
        </div>

        <div>
          <label
            htmlFor="reservoir_monitor"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Monitor de Reservatório (RM)
          </label>
          <select
            id="reservoir_monitor"
            value={formData.reservoir_monitor}
            onChange={handleSelectChange("reservoir_monitor")}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
          >
            <option value="">Nenhum</option>
            {rmDevices.map((device: any) => (
              <option
                key={device.id}
                value={device.id}
                disabled={
                  usedRMDeviceIds.includes(device.id) &&
                  device.id !== formData.reservoir_monitor
                }
              >
                {device.identifier} - {device.model}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label
            htmlFor="water_pump"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Bomba de Serviço
          </label>
          <select
            id="water_pump"
            value={formData.water_pump}
            onChange={handleSelectChange("water_pump")}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
          >
            <option value="">Nenhuma</option>
            {servicePumps.map((pump: any) => (
              <option
                key={pump.id}
                value={pump.id}
                disabled={
                  usedPumpIds.includes(pump.id) &&
                  pump.id !== formData.water_pump
                }
              >
                {pump.label} - {pump.identifier}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label
            htmlFor="notes"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Observações
          </label>
          <textarea
            id="notes"
            value={formData.notes}
            onChange={handleInputChange("notes")}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
            placeholder="Observações adicionais"
          />
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="enabled"
            checked={formData.enabled}
            onChange={handleCheckboxChange("enabled")}
            className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
          />
          <label htmlFor="enabled" className="ml-2 block text-sm text-gray-900">
            Ativo
          </label>
        </div>

        <ModalFooter
          primaryAction={{
            label: loading ? "Salvando..." : isEditing ? "Atualizar" : "Criar",
            onClick: () => handleSubmit({} as React.FormEvent),
            disabled: loading || !formData.name.trim(),
            loading: loading,
          }}
          secondaryAction={{
            label: "Cancelar",
            onClick: handleCancel,
          }}
        />
      </form>
    </div>
  );
}
