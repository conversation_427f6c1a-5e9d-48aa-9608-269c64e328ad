# Task list info:

- name: 250804_01
- base_branch: develop

---

# Task list context:

This task list is for planning the implementation of the MQTT integration with LIC devices.
The integration is made by exchanging protobuf messages via MQTT.
The protobuf encoding/decoding is done by the `protobuf` package and used in the `mqtt-integration` package.
The `mqtt-integration` package is responsible for connecting to the MQTT broker, subscribing to the topics, and handling the messages.

Protobuf encoding/decoding and MQTT broker connection, message receiving and dispatching are already implemented.

---

# Tasks

## Task 1. Identify what database changes will generate a new LIC configuration

**Description**
IncomingPacket declared in protobuf/proto/incoming_packet.proto is the message sent to the LIC to configure it. Its payload is one of the messages imported in the same file. For example, ConfigPackage, which is declared in protobuf/proto/config.proto.
We need to identify what changes in the database will generate a new LIC configuration and what is the corresponding protobuf message.
The result of this task is a document with the following information:

- For each database change that will generate a new LIC configuration, we must identify:
  - The database table fields that must be monitored for changes?
  - What is the corresponding protobuf message?
  - What are the fields in the database that must be sent to the LIC?
  - What are the corresponding database triggers and functions?

The resulting file will be tasks/analysis/LIC_CONFIGURATION_CHANGES.md

**Target directories**

- docs (documentation, in particular DDL.md)
- protobuf/proto (protobuf messages)

**Status:** Done

## Task 2. Implement a function for creating codec.in\_.config.ConfigPackage

**Description**
Implement a function that, given a LIC identifier, creates a codec.in\_.config.ConfigPackage message with the current configuration for that LIC.

- The function must be implemented in the file mqtt-integration/src/proto/builder/config.ts.
- tasks/analysis/PROTOBUF_MQTT_INTEGRATION_GUIDE.md has a section "#### 1.1 ConfigPackage - System Configuration" that has information about the message structure and database fields.
- docs/001-ENTITIES.md has information about the database schema and how properties are related to LIC devices.

**Target directories**

- mqtt-integration/src/proto/builder/config.ts

**Status:** Done

## Task 3. LIC WIFI configuration should be stored in property_device metadata instead of device metadata

**Description**
The LIC WIFI configuration (SSID and password) is currently stored in the device metadata. This is incorrect because the same LIC can be associated with different properties and thus have different WIFI configurations. The WIFI configuration must be stored in the property_device metadata instead.
if WIFI configuration has changed, updatePropertyDeviceAtom must be called, instead of updateDeviceAtom, in order to update the WIFI configuration in app/src/pages/main/components/DeviceDetailModal.tsx.

**Target directories**

- app (frontend)
- mqtt-integration (backend)

**Status:** Pending

## Task 4. property_device label should be editable in the frontend

**Description**
The property_device label is currently not editable in the frontend. It should be editable.
A new input field should be added to app/src/pages/main/components/DeviceDetailModal.tsx to edit the label. The label is a nullabe existing field in the property_device table.
If the label is changed, updatePropertyDeviceAtom must be called in order to update the label in the database.

**Target directories**

- app (frontend)

**Status:** Pending
