Include these information:

- How the timestamp based update columns of codecs work? What triggers their update to new values? What messages are sent to the device when they are updated? What configurations, database data and other elements are used to build those messages?

- How are received MQTT messages used? What effect each of them has on the database and the app's internal state?

- A diagram of why and how messages are constructed and sent to the device

- A diagram of how messages are received from the device and processed