import knexFactory, { K<PERSON> } from "knex";

/**
 * Create a Knex instance for tests using env:
 * TEST_DB_HOST, TEST_DB_PORT, TEST_DB_USER, TEST_DB_PASSWORD, TEST_DB_DATABASE
 */
export function createKnex(): Knex {
  const {
    TEST_DB_HOST,
    TEST_DB_PORT,
    TEST_DB_USER,
    TEST_DB_PASSWORD,
    TEST_DB_DATABASE,
  } = process.env;

  if (!TEST_DB_HOST || !TEST_DB_USER || !TEST_DB_DATABASE) {
    throw new Error(
      "Missing TEST DB env vars. Required: TEST_DB_HOST, TEST_DB_USER, TEST_DB_DATABASE. Optional: TEST_DB_PASSWORD, TEST_DB_PORT"
    );
  }

  const port =
    typeof TEST_DB_PORT === "string" ? parseInt(TEST_DB_PORT, 10) : 5432;

  return knexFactory({
    client: "pg",
    connection: {
      host: TEST_DB_HOST,
      port,
      user: TEST_DB_USER,
      password: TEST_DB_PASSWORD,
      database: TEST_DB_DATABASE,
    },
    pool: { min: 0, max: 5 },
  });
}

/**
 * Begin a transaction for a test. Must be rolled back by the caller.
 */
export async function begin(trxOrKnex?: Knex): Promise<Knex.Transaction> {
  const knex = trxOrKnex ?? createKnex();
  return knex.transaction();
}

/**
 * Rollback and destroy (safely) after a test.
 */
export async function rollbackAndDestroy(trx: Knex.Transaction): Promise<void> {
  try {
    await trx.rollback();
  } catch {
    // ignore
  }
  try {
    await (trx as unknown as Knex).destroy?.();
  } catch {
    // ignore
  }
}
