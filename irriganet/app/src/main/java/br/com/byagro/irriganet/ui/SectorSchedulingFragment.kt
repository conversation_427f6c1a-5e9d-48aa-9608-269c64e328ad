package br.com.byagro.irriganet.ui

import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import android.util.Log
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.navigation.fragment.findNavController
import br.com.byagro.irriganet.DBHelper
import br.com.byagro.irriganet.MainActivity
import br.com.byagro.irriganet.MeshDeviceItem
import br.com.byagro.irriganet.SectorSchedulingAdapter
import br.com.byagro.irriganet.SectorSchedulingItem
import br.com.byagro.irriganet.SectorSchedulingItemTouch
import br.com.byagro.irriganet.SharedData
import br.com.byagro.irriganet.WebService
import br.com.byagro.irriganet.databinding.FragmentSectorSchedulingBinding
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking

// TODO: Rename parameter arguments, choose names that match
// the fragment initialization parameters, e.g. ARG_ITEM_NUMBER
private const val ARG_PARAM1 = "param1"
private const val ARG_PARAM2 = "param2"

/**
 * A simple [Fragment] subclass.
 * Use the [SectorSchedulingFragment.newInstance] factory method to
 * create an instance of this fragment.
 */
class SectorSchedulingFragment : Fragment() {
    // TODO: Rename and change types of parameters
    private val PREFS_NAME = "byagro_prefs"
    private lateinit var binding: FragmentSectorSchedulingBinding
    private lateinit var sharedPref: SharedPreferences
    private lateinit var dbHelper: DBHelper

    private var groupIdx: Int = -1
    private var schedulingIdx: Int = -1
    private var fertiDeviceIdx: Int? = null
    private var backWashDeviceIdx: Int? = null

    private lateinit var adapter: SectorSchedulingAdapter
    private var sectorSchedulingList: MutableList<SectorSchedulingItem> = mutableListOf()
    var debugStartTime: Long = 0
    var debugEndTime: Long = 0
    var enableScheduling = false

    private val dayChips by lazy {
        listOf(
            binding.fragSectorSchedulingChipSunday,    // bit 0
            binding.fragSectorSchedulingChipMonday,    // bit 1
            binding.fragSectorSchedulingChipTuesday,   // bit 2
            binding.fragSectorSchedulingChipWednesday, // bit 3
            binding.fragSectorSchedulingChipThursday,  // bit 4
            binding.fragSectorSchedulingChipFriday,    // bit 5
            binding.fragSectorSchedulingChipSaturday   // bit 6
        )
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            groupIdx = it.getInt("groupIdx", -1)
            schedulingIdx = it.getInt("schedulingIdx", -1)
        }
        dbHelper = DBHelper(requireContext())
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentSectorSchedulingBinding.inflate(inflater, container, false)

        try {
            sharedPref = requireContext().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

            val activity = activity as? MainActivity

            var sectorSchedulingAuxList: MutableList<SectorSchedulingItem> = mutableListOf()
            var schedulingItems: Map<String, Any>? = mutableMapOf()
            if (schedulingIdx != -1) {
                debugStartTime = System.nanoTime()
                schedulingItems = dbHelper.getScheduling(schedulingIdx)
                sectorSchedulingAuxList = dbHelper.getAllSectorSchedulings(schedulingIdx);
                debugEndTime = System.nanoTime()
                Log.i("DB_TIMING", "D1: ${(debugEndTime - debugStartTime) / 1e6} ms")
            }

            sectorSchedulingList.clear()
            binding.fragSectorSchedulingRecyclerView.layoutManager = LinearLayoutManager(requireContext())
            adapter = SectorSchedulingAdapter(
                sectorSchedulingList,
                (schedulingItems?.get("allow_ferti") as? Int ?: 0) == 1
            )
            binding.fragSectorSchedulingRecyclerView.adapter = adapter

            val callback = SectorSchedulingItemTouch(adapter)
            val itemTouchHelper = ItemTouchHelper(callback)
            itemTouchHelper.attachToRecyclerView(binding.fragSectorSchedulingRecyclerView)

            debugStartTime = System.nanoTime()
            fertiDeviceIdx = dbHelper.getFertiDeviceIdxByGroup(groupIdx)
            backWashDeviceIdx = dbHelper.getDeviceIdxByMeshWithBackwash(groupIdx)
            debugEndTime = System.nanoTime()
            Log.i("DB_TIMING", "D2: ${(debugEndTime - debugStartTime) / 1e6} ms")
            if (fertiDeviceIdx == null) {
                binding.fragSectorSchedulingSwitchFerti.visibility = View.GONE
            } else {
                adapter.setShowFerti((schedulingItems?.get("allow_ferti") as? Int ?: 0) == 1)
            }
            if (backWashDeviceIdx == null) {
                binding.fragSectorSchedulingSwitchBackwash.visibility = View.GONE
            }

            binding.fragSectorSchedulingTextInputName.setText((schedulingItems?.get("name") as? String ?: ""))
            binding.fragSectorSchedulingInputHour.setText((schedulingItems?.get("hour") as? Int ?: 0).toString())
            binding.fragSectorSchedulingInputMinute.setText((schedulingItems?.get("min") as? Int ?: 0).toString())
            binding.fragSectorSchedulingSwitchFerti.isChecked = (schedulingItems?.get("allow_ferti") as? Int ?: 0) == 1
            binding.fragSectorSchedulingSwitchBackwash.isChecked = (schedulingItems?.get("allow_backwash") as? Int ?: 0) == 1

            val daysOfWeek = schedulingItems?.get("days_of_week") as? Int ?: 0
            enableScheduling = schedulingItems?.get("enabled") as? Boolean ?: false
            setChipsFromDaysOfWeek(daysOfWeek)

            val devicesList = dbHelper.getAllDevicesByGroup(groupIdx)
            val sectorDevicesCount = devicesList.count {
                it["type"]?.toString()?.toIntOrNull() == 0
            }

            if (schedulingIdx == -1 || sectorSchedulingAuxList.size == 0 || sectorSchedulingAuxList.size != sectorDevicesCount) {
                enableScheduling = true
                debugStartTime = System.nanoTime()
                debugEndTime = System.nanoTime()
                Log.i("DB_TIMING", "D3: ${(debugEndTime - debugStartTime) / 1e6} ms")
                devicesList.forEachIndexed { order, device ->
                    val type = device["type"]?.toString()?.toIntOrNull() ?: 0
                    if (type == 0) {
                        val sector = device["sector"]?.toString() ?: "!" // sector is the name
                        val deviceIdx = (device["idx"] as? Number)?.toInt() ?: 0
                        val state = false
                        val duration = "0"
                        val ferti = "0"
                        val fertiDelay = "0"
                        sectorSchedulingList.add(
                            SectorSchedulingItem(
                                deviceIdx,
                                type,
                                order,
                                state,
                                sector,
                                duration,
                                ferti,
                                fertiDelay
                            )
                        )
                    }
                }
            } else {
                sectorSchedulingList.addAll(sectorSchedulingAuxList);
            }
            //adapter.notifyItemInserted(sectorSchedulingList.size - 1)
            binding.fragSectorSchedulingSwitchFerti.setOnCheckedChangeListener { _, isChecked ->
                adapter.setShowFerti(isChecked)
            }
        } catch (e: Exception){
            e.printStackTrace()
        }

        binding.fragSectorSchedulingFabSave.setOnClickListener {
            try {
                val name = binding.fragSectorSchedulingTextInputName.text.toString()
                val hour = binding.fragSectorSchedulingInputHour.text.toString()
                val min = binding.fragSectorSchedulingInputMinute.text.toString()
                val fertiWash = sharedPref.getString("fertiWash", "0")

                if (hour.isEmpty() || min.isEmpty() || name.isEmpty()) {
                    Toast.makeText(
                        requireContext(),
                        "Preencha todos os campos!",
                        Toast.LENGTH_SHORT
                    ).show()
                    return@setOnClickListener
                }

                val newDaysOfWeek = getDaysOfWeekFromChips()
                if (newDaysOfWeek == 0) {
                    Toast.makeText(
                        requireContext(),
                        "Selecione os dias da semana!",
                        Toast.LENGTH_SHORT
                    ).show()
                    return@setOnClickListener
                }

                verifyAndUpdateItems()

                val validateSectorItems = validateSectorSchedulingItems(fertiWash?.toInt()?: 0)
                if (validateSectorItems.size > 0) {
                    if(binding.fragSectorSchedulingSwitchFerti.isChecked) {
                        Toast.makeText(
                            requireContext(),
                            "Verifique se os campos estão preenchidos, ou se o tempo da ferti considerando o atraso e a lavagem não pode ser maior que o tempo de irrigação do setor!",
                            Toast.LENGTH_LONG
                        ).show()
                    }else{
                        Toast.makeText(
                            requireContext(),
                            "Verifique se os campos estão preenchidos!",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                    return@setOnClickListener
                }

                val sectorSchedulingsToInsert = mutableListOf<Map<String, Any?>>()
                val deviceSchedulingsToInsert = mutableListOf<Map<String, Any?>>()

                val startTime: Int = (hour.toInt() * 3600) + (min.toInt() * 60)
                var time: Int = startTime
                var elapsedTime: Int = 0
                var reorder: Int = 0

                val isTimeInside = dbHelper.isTimeInsideExistingInterval(schedulingIdx ,groupIdx, startTime, endTime = startTime + elapsedTime, newDaysOfWeek)
                if(isTimeInside == false) {
                    if (schedulingIdx == -1) {
                        schedulingIdx = dbHelper.insertScheduling(
                            groupIdx,
                            binding.fragSectorSchedulingTextInputName.text.toString(),
                            hour.toInt(),
                            min.toInt(),
                            startTime,
                            startTime + elapsedTime,
                            newDaysOfWeek,
                            binding.fragSectorSchedulingSwitchFerti.isChecked,
                            binding.fragSectorSchedulingSwitchBackwash.isChecked
                        ).toInt()
                    } else {
                        dbHelper.deleteSectorSchedulings(schedulingIdx)
                        dbHelper.deleteDeviceSchedulings(schedulingIdx)
                    }

                    sectorSchedulingList.forEachIndexed { index, sector ->
                        val schedulingMap = mutableMapOf<String, Any?>(
                            "scheduling_idx" to schedulingIdx,
                            "device_idx" to sector.deviceIdx,
                            "n_order" to index,
                            "enabled" to if (sector.enabled) 1 else 0,
                            "type" to 0,
                            "ferti" to if (sector.ferti.isEmpty()) 0 else sector.ferti.toInt(),
                            "ferti_delay" to if (sector.fertiDelay.isEmpty()) 0 else sector.fertiDelay.toInt(),
                            "working_time" to sector.duration.toInt()
                        )
                        sectorSchedulingsToInsert.add(schedulingMap)
                    }

                    sectorSchedulingsToInsert.forEachIndexed { index, sectorScheduling ->
                        if (sectorScheduling["enabled"] as? Int == 1) {
                            val schedulingMap = mutableMapOf<String, Any?>(
                                "scheduling_idx" to schedulingIdx,
                                "device_idx" to sectorScheduling["device_idx"],
                                "n_order" to reorder,
                                "status" to 0,
                                "type" to sectorScheduling["type"],
                                "time" to time,
                                "sector_working_time" to sectorScheduling["working_time"],
                                "ferti_working_time" to sectorScheduling["ferti"],
                                "ferti_delay" to sectorScheduling["ferti_delay"],
                            )

                            elapsedTime += (sectorScheduling["working_time"] as? Int ?: 0) * 60
                            time += elapsedTime
                            if (index < sectorSchedulingsToInsert.lastIndex) {
                                elapsedTime += 30
                                time += 30
                            }
                            reorder += 1
                            deviceSchedulingsToInsert.add(schedulingMap)
                        }
                    }

                    if (schedulingIdx != -1) {
                        var waterpumpIdx: Int? = null
                        var waterpumpWorkingTime: Int? = null
                        var fertiIdx: Int? = null
                        var backwashIdx: Int? = null
                        val endTime: Int = startTime + elapsedTime
                        if (deviceSchedulingsToInsert.size > 0) { // Bomba de Irrigação
                            val devicesList = dbHelper.getAllDevicesByGroup(groupIdx)
                            val pumpDeviceType = devicesList.find { it["type"]?.toString() == MeshDeviceFragment.DevType.IrrigationPump.value.toString() }
                            if (!pumpDeviceType.isNullOrEmpty()) {
                                waterpumpIdx = pumpDeviceType?.get("idx") as? Int
                                waterpumpWorkingTime = elapsedTime / 60
                            }

                            val fertiDeviceType = devicesList.find { it["type"]?.toString() == MeshDeviceFragment.DevType.Ferti.value.toString() }
                            if (!fertiDeviceType.isNullOrEmpty()) {
                                fertiIdx = fertiDeviceType?.get("idx") as? Int
                            }

                            val backwashDeviceType = devicesList.find { it["type"]?.toString() == MeshDeviceFragment.DevType.Backwash.value.toString() }
                            if (!backwashDeviceType.isNullOrEmpty()) {
                                backwashIdx = backwashDeviceType?.get("idx") as? Int
                            }
                        }

                        if(deviceSchedulingsToInsert.size == 0) {
                            enableScheduling = false
                        }else{
                            enableScheduling = true
                        }

                        dbHelper.updateScheduling(
                            schedulingIdx,
                            groupIdx,
                            binding.fragSectorSchedulingTextInputName.text.toString(),
                            hour.toInt(),
                            min.toInt(),
                            startTime,
                            endTime,
                            newDaysOfWeek,
                            reorder,
                            binding.fragSectorSchedulingSwitchFerti.isChecked,
                            binding.fragSectorSchedulingSwitchBackwash.isChecked,
                            waterpumpIdx,
                            waterpumpWorkingTime,
                            fertiIdx,
                            backwashIdx,
                            enableScheduling
                        )
                    }

                    debugStartTime = System.nanoTime()
                    dbHelper.insertSectorSchedulings(sectorSchedulingsToInsert)
                    dbHelper.insertDeviceSchedulings(deviceSchedulingsToInsert)
                    debugEndTime = System.nanoTime()
                    Log.i("DB_TIMING", "D5: ${(debugEndTime - debugStartTime) / 1e6} ms")

                    val timeStamp = System.currentTimeMillis() / 1000
                    val codecIdx = dbHelper.getCodecIdxByGroup(groupIdx)

                    dbHelper.updateCodecFields(
                        codecIdx ?: 0, mapOf(
                            "last_scheduling_update" to timeStamp,
                            "last_device_scheduling_update" to timeStamp,
                            "last_config_update" to timeStamp
                        )
                    )
                    with(sharedPref.edit()) {
                        putBoolean("dataUpdated", true)
                        putBoolean("dataUpdateEvent", true)
                        apply()
                    }

                    findNavController().popBackStack()
                } else {
                    Toast.makeText(
                        requireContext(),
                        "Está ocorrendo uma sobreposição de intervalo! Verifique o horário de início e término!",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            } catch (e: Exception){
                e.printStackTrace()
            }
        }

        return binding.root
    }

    fun verifyAndUpdateItems() {
        for (i in sectorSchedulingList.indices) {
            try {
                val item = sectorSchedulingList[i]
                val viewHolder =
                    binding.fragSectorSchedulingRecyclerView.findViewHolderForAdapterPosition(i) as? SectorSchedulingAdapter.SchedulingViewHolder
                        ?: continue

                val currentState = viewHolder.binding.checkBoxSetor.isChecked
                val currentDuration = viewHolder.binding.textInputDuration.text.toString()
                val currentFerti = viewHolder.binding.textInputFerti.text.toString()
                val currentFertiDelay = viewHolder.binding.textInputFertiDelay.text.toString()

                if (item.enabled != currentState || item.duration != currentDuration ||
                    item.ferti != currentFerti || item.fertiDelay != currentFertiDelay
                ) {

                    val updatedItem = item.copy(
                        enabled = currentState,
                        duration = currentDuration,
                        ferti = currentFerti,
                        fertiDelay = currentFertiDelay
                    )

                    sectorSchedulingList[i] = updatedItem
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    /**
     * (bit -> day):
     *  - 0: Sunday
     *  - 1: Monday
     *  - 2: Tuesday
     *  - 3: Wednesday
     *  - 4: Thursday
     *  - 5: Friday
     *  - 6: Saturday
     */
    private fun setChipsFromDaysOfWeek(daysOfWeek: Int) {
        dayChips.forEachIndexed { i, chip ->
            chip.isChecked = ((daysOfWeek shr i) and 1) == 1
        }
    }

    // Domingo é o bit0
    private fun getDaysOfWeekFromChips(): Int {
        var result = 0
        dayChips.forEachIndexed { i, chip ->
            if (chip.isChecked) {
                result = result or (1 shl i)
            }
        }
        return result
    }

    private fun validateSectorSchedulingItems(fertiWash: Int): List<Int> {
        val invalidIndexes = mutableListOf<Int>()

        sectorSchedulingList.forEachIndexed { index, item ->
            try {
                if (!item.enabled) return@forEachIndexed

                if (item.duration.isNullOrBlank() || (binding.fragSectorSchedulingSwitchFerti.isChecked && (item.ferti.isNullOrBlank() || item.fertiDelay.isNullOrBlank()))) {
                    invalidIndexes.add(index)
                    return@forEachIndexed
                }

                val workingTime = item.duration.toIntOrNull() ?: 0
                val fertiTime = item.ferti.toIntOrNull() ?: 0
                val fertiDelay = item.fertiDelay.toIntOrNull() ?: 0

                if (workingTime == 0 || (binding.fragSectorSchedulingSwitchFerti.isChecked && ((workingTime < fertiTime + fertiDelay + fertiWash) || fertiTime == 0))) {
                    invalidIndexes.add(index)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        return invalidIndexes
    }
}