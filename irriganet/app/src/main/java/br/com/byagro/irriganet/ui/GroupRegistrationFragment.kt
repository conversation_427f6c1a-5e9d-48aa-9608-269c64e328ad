package br.com.byagro.irriganet.ui

import android.app.AlertDialog
import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.RecyclerView
import br.com.byagro.irriganet.DBHelper
import br.com.byagro.irriganet.GroupAdapter
import br.com.byagro.irriganet.GroupItem
import br.com.byagro.irriganet.R
import br.com.byagro.irriganet.databinding.FragmentGroupRegistrationBinding

// TODO: Rename parameter arguments, choose names that match
// the fragment initialization parameters, e.g. ARG_ITEM_NUMBER

/**
 * A simple [Fragment] subclass.
 * Use the [GroupRegistrationFragment.newInstance] factory method to
 * create an instance of this fragment.
 */
class GroupRegistrationFragment : Fragment() {
    private lateinit var binding: FragmentGroupRegistrationBinding
    private lateinit var dbHelper: DBHelper

    private var codecIdx: Int = 0
    private var codecIdentity: String? = null
    private var codecName: String? = null

    private lateinit var recyclerView: RecyclerView
    private lateinit var adapter: GroupAdapter
    private var groupList: MutableList<GroupItem> = mutableListOf()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            codecIdx = it.getInt("codecIdx")
            codecIdentity = it.getString("codecIdentity")
            codecName = it.getString("codecName")
        }
        dbHelper = DBHelper(requireContext())
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {

        //val view = inflater.inflate(R.layout.fragment_codec, container, false)
        binding = FragmentGroupRegistrationBinding.inflate(inflater, container, false)

        try {
            //recyclerView = view.findViewById(R.id.recycler_view_groups)
            binding.fragGroupRegRecyclerViewGroups.layoutManager = LinearLayoutManager(context)

            adapter = GroupAdapter(
                groupList,
                { selectedItem -> onItemClick(selectedItem) },
                { position -> deleteGroup(position) })
            binding.fragGroupRegRecyclerViewGroups.adapter = adapter

            binding.fragGroupRegFabAddGroup.setOnClickListener {
                showGroupInputDialog()
            }

            //binding.fragCodecTextViewCodecId.setText("Codec "+codecId)

            groupList.clear()
            groupList.addAll(dbHelper.getAllGroups(codecIdx))
            adapter.notifyDataSetChanged()
        } catch (e: Exception) {
            e.printStackTrace()
        }

        return binding.root
    }

    private fun showGroupInputDialog() {
        // Inflate the custom layout
        val dialogView = LayoutInflater.from(context).inflate(R.layout.dialog_group_input, null)
        val editText: EditText = dialogView.findViewById(R.id.editText)
        val next_id = dbHelper.getNextGroupIdx()
        var name = "Talhão " + next_id.toString()

        editText.setText(name)

        // Build the AlertDialog
        val dialogBuilder = AlertDialog.Builder(requireContext())
            .setView(dialogView)
            .setTitle("Grupo")
            .setPositiveButton("OK") { dialog, _ ->
                val inputText = editText.text.toString()
                if (inputText.isNotEmpty()) {
                    val idx = dbHelper.insertGroup(editText.text.toString(), codecIdx).toInt()
                    groupList.add(GroupItem(idx, inputText))
                    adapter.notifyItemInserted(groupList.size - 1)
                } else {

                }
                dialog.dismiss()
            }
            .setNegativeButton("Cancel") { dialog, _ ->
                dialog.cancel()
            }

        // Create and show the dialog
        val alertDialog = dialogBuilder.create()
        alertDialog.show()
    }

    private fun onItemClick(Item: GroupItem) {
        val bundle = Bundle().apply {
            putInt("codecIdx", codecIdx) // indice do banco de dados
            putInt("groupIdx", Item.idx)
            putString("groupName", Item.name)
        }
        findNavController().navigate(R.id.action_nav_codec_to_nav_group, bundle)
    }

    private fun deleteGroup(position: Int) {
        val builder = AlertDialog.Builder(requireContext())
        builder.setTitle("Confirmar Exclusão")
        builder.setMessage("Tem certeza de que deseja excluir este Grupo? Todos os dispositivos vinculados a ele também serão removidos permanentemente!")

        builder.setPositiveButton("Sim") { dialog, _ ->
            val groupId = groupList[position].idx
            val rowsDeleted = dbHelper.deleteGroup(groupId)

            if (rowsDeleted > 0) {
                groupList.removeAt(position) // Remove item from the list
                adapter.notifyItemRemoved(position) // Notify adapter about the change
            }
            dialog.dismiss()
        }

        builder.setNegativeButton("Cancelar") { dialog, _ ->
            dialog.dismiss() // Close dialog without deleting
        }

        val alertDialog = builder.create()
        alertDialog.show()
    }
}