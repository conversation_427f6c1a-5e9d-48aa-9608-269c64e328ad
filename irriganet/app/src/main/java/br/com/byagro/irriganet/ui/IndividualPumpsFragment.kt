package br.com.byagro.irriganet.ui

import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import br.com.byagro.irriganet.DBHelper
import br.com.byagro.irriganet.DeviceItem
import br.com.byagro.irriganet.SimpleItem
import br.com.byagro.irriganet.IndividualPumpsAdapter
import br.com.byagro.irriganet.R
import br.com.byagro.irriganet.databinding.FragmentIndividualPumpsBinding

// TODO: Rename parameter arguments, choose names that match
// the fragment initialization parameters, e.g. ARG_ITEM_NUMBER
private const val ARG_PARAM1 = "param1"
private const val ARG_PARAM2 = "param2"

/**
 * A simple [Fragment] subclass.
 * Use the [IndividualPumpsFragment.newInstance] factory method to
 * create an instance of this fragment.
 */
class IndividualPumpsFragment : Fragment() {
    // TODO: Rename and change types of parameters
    private lateinit var binding: FragmentIndividualPumpsBinding
    private lateinit var dbHelper: DBHelper

    private lateinit var adapter: IndividualPumpsAdapter
    private var pumpsList: MutableList<DeviceItem> = mutableListOf()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {

        }
        dbHelper = DBHelper(requireContext())
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentIndividualPumpsBinding.inflate(inflater, container, false)

        try {
            //(activity as AppCompatActivity).supportActionBar!!.title = "Bombas Avulsas"
            binding.fragIndividualPumpsRecyclerViewPumps.layoutManager = LinearLayoutManager(context)

            adapter = IndividualPumpsAdapter(pumpsList, { selectedItem -> onItemClick(selectedItem) })
            binding.fragIndividualPumpsRecyclerViewPumps.adapter = adapter

            pumpsList.clear()
            pumpsList.addAll(dbHelper.getAllDevicesByType(3))
            adapter.notifyDataSetChanged()
        } catch (e: Exception){
            e.printStackTrace()
        }

        return binding.root
    }

    private fun onItemClick(Item: DeviceItem) {
        val bundle = Bundle().apply {
            putInt("deviceIdx", Item.idx)
        }
        findNavController().navigate(R.id.action_nav_individual_pumps_to_nav_pump_control, bundle)
    }

}