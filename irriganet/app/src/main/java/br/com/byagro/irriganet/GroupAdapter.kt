package br.com.byagro.irriganet

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView

class GroupAdapter(
    private val groupList: MutableList<GroupItem>,
    private val onItemClick: (GroupItem) -> Unit,
    private val onDeleteClick: (Int) -> Unit,
) :
    RecyclerView.Adapter<GroupAdapter.GroupViewHolder>() {

    class GroupViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val textView: TextView = itemView.findViewById(R.id.item_text)
        val deleteButton: ImageButton = itemView.findViewById(R.id.btn_delete)

        fun bind(item: GroupItem, onItemClick: (GroupItem) -> Unit, onDeleteClick: (Int) -> Unit) {
            itemView.setOnClickListener {
                onItemClick(item)
            }

            deleteButton.setOnClickListener {
                onDeleteClick(adapterPosition)
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): GroupViewHolder {
        val itemView = LayoutInflater.from(parent.context).inflate(R.layout.item_model_text_btn, parent, false)
        return GroupViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: GroupViewHolder, position: Int) {
        val currentGroup = groupList[position]
        holder.textView.text = currentGroup.name
        holder.bind(currentGroup, onItemClick, onDeleteClick)
    }

    override fun getItemCount() = groupList.size
}