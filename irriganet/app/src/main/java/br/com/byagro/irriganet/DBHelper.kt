package br.com.byagro.irriganet

import android.content.ContentValues
import android.content.Context
import android.database.Cursor
import android.database.SQLException
import android.database.sqlite.SQLiteConstraintException
import android.database.sqlite.SQLiteDatabase
import android.database.sqlite.SQLiteOpenHelper
import br.com.byagro.irriganet.ui.MeshDeviceFragment
import com.google.gson.Gson

class DBHelper(context: Context) : SQLiteOpenHelper(context, DATABASE_NAME, null, DATABASE_VERSION) {

    override fun onCreate(db: SQLiteDatabase) {
        try {
            db.execSQL(CREATE_TABLE_CODECS)
            db.execSQL(CREATE_TABLE_GROUPS)
            db.execSQL(CREATE_TABLE_MESH_DEVICES)
            db.execSQL(CREATE_TABLE_DEVICES)
            db.execSQL(CREATE_TABLE_SCHEDULINGS)
            db.execSQL(CREATE_TABLE_SECTOR_SCHEDULINGS)
            db.execSQL(CREATE_TABLE_DEVICE_SCHEDULINGS)

            // Unique Index - Devices
            db.execSQL(CREATE_DEVICES_UNIQUE_INDEX)

            // Índices - Groups
            db.execSQL(CREATE_INDEX_GROUPS_CODEC_IDX)

            // Índices - Mesh_Devices
            db.execSQL(CREATE_INDEX_MESH_DEVICES_CODEC_IDX)
            db.execSQL(CREATE_INDEX_MESH_DEVICES_GROUP_IDX)

            // Índices - Devices
            db.execSQL(CREATE_INDEX_DEVICES_MESH_IDX)
            db.execSQL(CREATE_INDEX_DEVICES_TYPE)
            db.execSQL(CREATE_INDEX_DEVICES_SECTOR)

            // Índices - Schedulings
            db.execSQL(CREATE_INDEX_SCHEDULINGS_GROUP_IDX)

            // Índices - Sector_Schedulings
            db.execSQL(CREATE_INDEX_SECTOR_SCHEDULINGS_SCHEDULING_IDX)
            db.execSQL(CREATE_INDEX_SECTOR_SCHEDULINGS_DEVICE_IDX)

            // Índices - Device_Schedulings
            db.execSQL(CREATE_INDEX_DEVICE_SCHEDULINGS_SCHEDULING_IDX)
            db.execSQL(CREATE_INDEX_DEVICE_SCHEDULINGS_DEVICE_IDX)
        } catch (e: SQLException) {
            e.printStackTrace()
        }
    }

    override fun onUpgrade(db: SQLiteDatabase, oldVersion: Int, newVersion: Int) {
        try {
            db.execSQL("DROP INDEX IF EXISTS idx_groups_codec_idx")

            db.execSQL("DROP INDEX IF EXISTS idx_mesh_devices_codec_idx")
            db.execSQL("DROP INDEX IF EXISTS idx_mesh_devices_group_idx")

            db.execSQL("DROP INDEX IF EXISTS idx_devices_mesh_idx")
            db.execSQL("DROP INDEX IF EXISTS idx_devices_type")
            db.execSQL("DROP INDEX IF EXISTS idx_devices_sector")

            db.execSQL("DROP INDEX IF EXISTS idx_schedulings_group_idx")

            db.execSQL("DROP INDEX IF EXISTS idx_sector_schedulings_scheduling_idx")
            db.execSQL("DROP INDEX IF EXISTS idx_sector_schedulings_device_idx")

            db.execSQL("DROP INDEX IF EXISTS idx_device_schedulings_scheduling_idx")
            db.execSQL("DROP INDEX IF EXISTS idx_device_schedulings_device_idx")

            db.execSQL("DROP TABLE IF EXISTS $TABLE_DEVICE_SCHEDULINGS")
            db.execSQL("DROP TABLE IF EXISTS $TABLE_SECTOR_SCHEDULINGS")
            db.execSQL("DROP TABLE IF EXISTS $TABLE_SCHEDULINGS")

            db.execSQL("DROP TABLE IF EXISTS $TABLE_DEVICES")
            db.execSQL("DROP TABLE IF EXISTS $TABLE_MESH_DEVICES")
            db.execSQL("DROP TABLE IF EXISTS $TABLE_GROUPS")
            db.execSQL("DROP TABLE IF EXISTS $TABLE_CODECS")

            onCreate(db)
        } catch (e: SQLException) {
            e.printStackTrace()
        }
    }

    override fun onOpen(db: SQLiteDatabase) {
        super.onOpen(db)
        db.execSQL("PRAGMA foreign_keys = ON;")
    }

    override fun onConfigure(db: SQLiteDatabase) {
        try {
            super.onConfigure(db)
            db.setForeignKeyConstraintsEnabled(true)
        } catch (e: SQLException) {
            e.printStackTrace()
        }
    }

    companion object {
        private const val DATABASE_NAME = "irriganet.db"
        private const val DATABASE_VERSION = 55

        private const val TABLE_CODECS = "Codecs"
        private const val TABLE_GROUPS = "Groups"
        private const val TABLE_MESH_DEVICES = "Mesh_Devices"
        private const val TABLE_DEVICES = "Devices"
        private const val TABLE_SCHEDULINGS = "Schedulings"
        private const val TABLE_SECTOR_SCHEDULINGS = "Sector_Schedulings"
        private const val TABLE_DEVICE_SCHEDULINGS = "Device_Schedulings"

        private const val CREATE_TABLE_CODECS = """
            CREATE TABLE $TABLE_CODECS (
                idx INTEGER PRIMARY KEY AUTOINCREMENT,
                identity TEXT NOT NULL,
                name TEXT NOT NULL,
                wifi_ssid TEXT,
                wifi_passwd TEXT,
                last_devices_update INTEGER,
                last_scheduling_update INTEGER,
                last_device_scheduling_update INTEGER,
                last_automation_update INTEGER,
                last_config_update INTEGER,
                enabled INTEGER DEFAULT 1
            )
        """

        private const val CREATE_TABLE_GROUPS = """
            CREATE TABLE $TABLE_GROUPS (
                idx INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                codec_idx INTEGER,
                FOREIGN KEY (codec_idx) REFERENCES $TABLE_CODECS(idx) ON DELETE CASCADE
            )
        """

        private const val CREATE_INDEX_GROUPS_CODEC_IDX =
            "CREATE INDEX IF NOT EXISTS idx_groups_codec_idx ON $TABLE_GROUPS(codec_idx)"

        private const val CREATE_TABLE_MESH_DEVICES = """
            CREATE TABLE $TABLE_MESH_DEVICES (
                idx INTEGER PRIMARY KEY AUTOINCREMENT,
                identity TEXT NOT NULL,
                name TEXT NOT NULL,
                type INTEGER DEFAULT 0,
                mode INTEGER DEFAULT 0,
                equipament INTEGER DEFAULT 0,
                check_input INTEGER DEFAULT 0,
                devices_bitmask INTEGER DEFAULT 0,
                level_pump_idx INTEGER DEFAULT 0,
                level_pump_enable INTEGER DEFAULT 0,
                level_pump_working_time INTEGER DEFAULT 0,
                codec_idx INTEGER,
                group_idx INTEGER,
                FOREIGN KEY (codec_idx) REFERENCES $TABLE_CODECS(idx) ON DELETE CASCADE,
                FOREIGN KEY (group_idx) REFERENCES $TABLE_GROUPS(idx) ON DELETE CASCADE
            )
        """

        private const val CREATE_INDEX_MESH_DEVICES_CODEC_IDX =
            "CREATE INDEX IF NOT EXISTS idx_mesh_devices_codec_idx ON $TABLE_MESH_DEVICES(codec_idx)"
        private const val CREATE_INDEX_MESH_DEVICES_GROUP_IDX =
            "CREATE INDEX IF NOT EXISTS idx_mesh_devices_group_idx ON $TABLE_MESH_DEVICES(group_idx)"

        private const val CREATE_TABLE_DEVICES = """
            CREATE TABLE $TABLE_DEVICES (
                idx INTEGER PRIMARY KEY AUTOINCREMENT,
                ord_idx INTEGER NOT NULL,
                mesh_idx INTEGER NOT NULL,
                identity TEXT NOT NULL,
                type INTEGER NOT NULL,
                out1 INTEGER,
                out2 INTEGER,
                input INTEGER,
                mode INTEGER,
                sector INTEGER,
                eqpt_ver INTEGER,
                FOREIGN KEY (mesh_idx) REFERENCES $TABLE_MESH_DEVICES(idx) ON DELETE CASCADE
            );
        """

        private const val CREATE_INDEX_DEVICES_MESH_IDX = """
            CREATE INDEX IF NOT EXISTS idx_devices_mesh_idx ON $TABLE_DEVICES(mesh_idx)
        """
        private const val CREATE_INDEX_DEVICES_SECTOR = """
            CREATE INDEX IF NOT EXISTS idx_devices_sector ON $TABLE_DEVICES(sector)
        """
        private const val CREATE_INDEX_DEVICES_TYPE = """
            CREATE INDEX IF NOT EXISTS idx_devices_type ON $TABLE_DEVICES(type)
        """
        private const val CREATE_DEVICES_UNIQUE_INDEX = """
            CREATE UNIQUE INDEX IF NOT EXISTS u_mesh_identity ON TABLE_DEVICES(mesh_idx, identity);
        """

        private const val CREATE_TABLE_SCHEDULINGS = """
            CREATE TABLE $TABLE_SCHEDULINGS (
                idx INTEGER PRIMARY KEY AUTOINCREMENT,
                ord_idx INTEGER NOT NULL,
                group_idx INTEGER NOT NULL,
                name TEXT NOT NULL,
                hour INTEGER NOT NULL,
                min INTEGER NOT NULL,
                start_time INTEGER NOT NULL,
                end_time INTEGER DEFAULT NULL,
                days_of_week INTEGER NOT NULL,
                number_of_steps INTEGER NOT NULL,
                allow_ferti INTEGER NOT NULL,
                allow_backwash INTEGER NOT NULL,
                waterpump_idx INTEGER DEFAULT NULL,
                waterpump_ord_idx INTEGER DEFAULT NULL,
                waterpump_working_time INTEGER DEFAULT NULL,
                ferti_idx INTEGER DEFAULT NULL,
                ferti_ord_idx INTEGER DEFAULT NULL,
                backwash_idx INTEGER DEFAULT NULL,
                backwash_ord_idx INTEGER DEFAULT NULL,
                enabled INTEGER DEFAULT 1,
                FOREIGN KEY (group_idx) REFERENCES $TABLE_GROUPS(idx) ON DELETE CASCADE
            )
        """

        private const val CREATE_INDEX_SCHEDULINGS_GROUP_IDX =
            "CREATE INDEX IF NOT EXISTS idx_schedulings_group_idx ON $TABLE_SCHEDULINGS(group_idx)"

        private const val CREATE_TABLE_SECTOR_SCHEDULINGS = """
            CREATE TABLE $TABLE_SECTOR_SCHEDULINGS (
                idx INTEGER PRIMARY KEY AUTOINCREMENT,
                scheduling_idx INTEGER NOT NULL,
                device_idx INTEGER NOT NULL,
                n_order INTEGER NOT NULL,
                enabled INTEGER NOT NULL,
                type INTEGER NOT NULL,  
                ferti INTEGER NOT NULL,
                ferti_delay INTEGER NOT NULL,
                working_time INTEGER NOT NULL,
                FOREIGN KEY (scheduling_idx) REFERENCES $TABLE_SCHEDULINGS(idx) ON DELETE CASCADE
            )
        """

        private const val CREATE_INDEX_SECTOR_SCHEDULINGS_SCHEDULING_IDX =
            "CREATE INDEX IF NOT EXISTS idx_sector_schedulings_scheduling_idx ON $TABLE_SECTOR_SCHEDULINGS(scheduling_idx)"
        private const val CREATE_INDEX_SECTOR_SCHEDULINGS_DEVICE_IDX =
            "CREATE INDEX IF NOT EXISTS idx_sector_schedulings_device_idx ON $TABLE_SECTOR_SCHEDULINGS(device_idx)"

        private const val CREATE_TABLE_DEVICE_SCHEDULINGS = """
            CREATE TABLE $TABLE_DEVICE_SCHEDULINGS (
                idx INTEGER PRIMARY KEY AUTOINCREMENT,
                ord_idx INTEGER NOT NULL,
                scheduling_idx INTEGER NOT NULL,
                device_idx INTEGER NOT NULL,
                n_order INTEGER NOT NULL,
                status INTEGER NOT NULL,
                type INTEGER NOT NULL,  
                time INTEGER NOT NULL,
                sector_working_time INTEGER NOT NULL,
                ferti_working_time INTEGER DEFAULT NULL,
                ferti_delay INTEGER NOT NULL,
                FOREIGN KEY (scheduling_idx) REFERENCES $TABLE_SCHEDULINGS(idx) ON DELETE CASCADE
            )
        """

        private const val CREATE_INDEX_DEVICE_SCHEDULINGS_SCHEDULING_IDX =
            "CREATE INDEX IF NOT EXISTS idx_device_schedulings_scheduling_idx ON $TABLE_DEVICE_SCHEDULINGS(scheduling_idx)"
        private const val CREATE_INDEX_DEVICE_SCHEDULINGS_DEVICE_IDX =
            "CREATE INDEX IF NOT EXISTS idx_device_schedulings_device_idx ON $TABLE_DEVICE_SCHEDULINGS(device_idx)"
    }

    fun insertCodec(identity: String, name: String, wifiSsid: String, wifiPasswd: String): Long {
        return try {
            val db = this.writableDatabase
            val values = ContentValues().apply {
                put("identity", identity)
                put("name", name)
                put("wifi_ssid", wifiSsid)
                put("wifi_passwd", wifiPasswd)
            }
            db.insert(TABLE_CODECS, null, values)
        } catch (e: SQLException) {
            e.printStackTrace()
            -1
        }
    }

    fun updateCodec(idx: Int, identity: String, name: String, wifiSsid: String, wifiPasswd: String, enabled: Int): Int {
        return try {
            val db = this.writableDatabase

            val values = ContentValues().apply {
                put("identity", identity)
                put("name", name)
                put("wifi_ssid", wifiSsid)
                put("wifi_passwd", wifiPasswd)
                put("enabled", enabled)
            }
            val whereClause = "idx = ?"
            val whereArgs = arrayOf(idx.toString())

            db.update(TABLE_CODECS, values, whereClause, whereArgs)
        } catch (e: SQLException) {
            e.printStackTrace()
            -1
        }
    }

    fun updateCodecFields(idx: Int, fieldsToUpdate: Map<String, Any>): Int {
        return try {
            val db = this.writableDatabase

            val values = ContentValues().apply {
                for ((key, value) in fieldsToUpdate) {
                    when (value) {
                        is String -> put(key, value)
                        is Int -> put(key, value)
                        is Long -> put(key, value)
                        is Float -> put(key, value)
                        is Double -> put(key, value)
                        is Boolean -> put(key, if (value) 1 else 0)
                        else -> throw IllegalArgumentException("Unsupported value type for column $key")
                    }
                }
            }

            val whereClause = "idx = ?"
            val whereArgs = arrayOf(idx.toString())

            db.update(TABLE_CODECS, values, whereClause, whereArgs)
        } catch (e: SQLException) {
            e.printStackTrace()
            -1
        }
    }


    fun deleteCodec(codecIdx: Int): Int {
        return try {
            val db = this.writableDatabase
            //db.delete(TABLE_GROUPS, "codec_idx = ?", arrayOf(codecIdx.toString()))
            //db.delete(TABLE_MESH_DEVICES, "codec_idx = ?", arrayOf(codecIdx.toString()))
            db.delete(TABLE_CODECS, "idx = ?", arrayOf(codecIdx.toString()))
        } catch (e: SQLException) {
            e.printStackTrace()
            0
        }
    }

    fun getAllCodecs(): List<CodecItem> {
        val codecList = mutableListOf<CodecItem>()
        return try {
            val db = this.readableDatabase
            val cursor: Cursor = db.rawQuery("SELECT idx, identity, name FROM $TABLE_CODECS", null)
            if (cursor.moveToFirst()) {
                do {
                    val idx = cursor.getInt(0)
                    val identity = cursor.getString(1)
                    val name = cursor.getString(2)
                    codecList.add(CodecItem(idx, identity, name))
                } while (cursor.moveToNext())
            }
            cursor.close()
            codecList
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList()
        }
    }

    fun getAllCodecsOnMap(): List<Map<String, Any>> {
        val codecList = mutableListOf<Map<String, Any>>() // Create a mutable list of maps
        return try {
            val db = this.readableDatabase
            val cursor: Cursor = db.rawQuery("SELECT idx, identity, name, wifi_ssid, wifi_passwd, last_devices_update, last_scheduling_update, " +
                    "last_device_scheduling_update, last_automation_update, last_config_update FROM $TABLE_CODECS WHERE enabled = 1", null)
            if (cursor.moveToFirst()) {
                do {
                    val idx = cursor.getInt(0)
                    val identity = cursor.getString(1)
                    val name = cursor.getString(2)
                    val wifiSsid = cursor.getString(3)
                    val wifiPasswd = cursor.getString(4)
                    val lastDevicesUpdate = cursor.getLong(5)
                    val lastSchedulingUpdate = cursor.getLong(6)
                    val lastDeviceSchedulingUpdate = cursor.getLong(7)
                    val lastAutomationUpdate = cursor.getLong(8)
                    val lastConfigUpdate = cursor.getLong(9)

                    // Create a map for the current row
                    val rowMap = mapOf(
                        "idx" to idx,
                        "identity" to identity,
                        "name" to name,
                        "wifi_ssid" to wifiSsid,
                        "wifi_passwd" to wifiPasswd,
                        "last_devices_update" to lastDevicesUpdate,
                        "last_scheduling_update" to lastSchedulingUpdate,
                        "last_device_scheduling_update" to lastDeviceSchedulingUpdate,
                        "last_automation_update" to lastAutomationUpdate,
                        "last_config_update" to lastConfigUpdate
                    )

                    // Add the map to the list
                    codecList.add(rowMap)
                } while (cursor.moveToNext())
            }
            cursor.close()
            codecList // Return the list of maps
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList() // Return an empty list in case of an error
        }
    }

    fun getCodecByIdx(idx: Int): Map<String, Any>? {
        val db = readableDatabase
        val query = "SELECT * FROM $TABLE_CODECS WHERE idx = ?"
        val cursor = db.rawQuery(query, arrayOf(idx.toString()))

        return if (cursor.moveToFirst()) {
            val result = mapOf(
                "identity" to cursor.getString(cursor.getColumnIndexOrThrow("identity")),
                "name" to cursor.getString(cursor.getColumnIndexOrThrow("name")),
                "wifi_ssid" to cursor.getString(cursor.getColumnIndexOrThrow("wifi_ssid")),
                "wifi_passwd" to cursor.getString(cursor.getColumnIndexOrThrow("wifi_passwd")),
                "enabled" to cursor.getInt(cursor.getColumnIndexOrThrow("enabled"))
            )
            cursor.close()
            db.close()
            result
        } else {
            cursor.close()
            db.close()
            null
        }
    }

    fun getCodecsCount(): Int {
        return try {
            val db = this.readableDatabase
            val cursor = db.rawQuery("SELECT COUNT(*) FROM $TABLE_CODECS", null)
            var count = 0
            if (cursor.moveToFirst()) {
                count = cursor.getInt(0)
            }
            cursor.close()
            count
        } catch (e: SQLException) {
            e.printStackTrace()
            0
        }
    }

    fun insertGroup(name: String, codecIdx: Int?): Long {
        return try {
            val db = this.writableDatabase
            val values = ContentValues().apply {
                put("name", name)
                put("codec_idx", codecIdx)
            }
            db.insert(TABLE_GROUPS, null, values)
        } catch (e: SQLException) {
            e.printStackTrace()
            -1
        }
    }

    fun getAllGroups(codecIdx: Int?): List<GroupItem> {
        val groupList = mutableListOf<GroupItem>()
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            cursor = if (codecIdx == null) {
                db.rawQuery("SELECT g.idx, g.name FROM $TABLE_GROUPS AS g JOIN $TABLE_CODECS AS c ON g.codec_idx = c.idx WHERE c.enabled = 1", null)
            } else {
                db.rawQuery("SELECT idx, name FROM $TABLE_GROUPS WHERE codec_idx = ?", arrayOf(codecIdx.toString()))
            }

            while (cursor.moveToNext()) {
                val idx = cursor.getInt(0)
                val name = cursor.getString(1)
                groupList.add(GroupItem(idx, name))
            }

            groupList
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList()
        } finally {
            cursor?.close()
        }
    }

    fun deleteGroup(groupIdx: Int): Int {
        return try {
            val db = this.writableDatabase
            db.delete(TABLE_MESH_DEVICES, "group_idx = ?", arrayOf(groupIdx.toString()))
            db.delete(TABLE_GROUPS, "idx = ?", arrayOf(groupIdx.toString()))
        } catch (e: SQLException) {
            e.printStackTrace()
            0
        }
    }

    fun getNextGroupIdx(): Int {
        val db = this.readableDatabase
        var cursor: Cursor? = null
        var nextIdx = 1

        return try {
            cursor = db.rawQuery("SELECT MAX(idx) FROM $TABLE_GROUPS", null)

            if (cursor.moveToFirst() && !cursor.isNull(0)) {
                nextIdx = cursor.getInt(0) + 1
            }

            nextIdx
        } catch (e: SQLException) {
            e.printStackTrace()
            1
        } finally {
            cursor?.close()
        }
    }

    fun insertScheduling(
        groupIdx: Int, name: String, hour: Int, min: Int, startTime: Int, endTime: Int, daysOfWeek: Int, allowFerti: Boolean, allowBackwash: Boolean
    ): Long {
        return try {
            val db = this.writableDatabase
            val values = ContentValues().apply {
                put("ord_idx", 0)
                put("group_idx", groupIdx)
                put("name", name)
                put("hour", hour)
                put("min", min)
                put("start_time", startTime)
                put("end_time", endTime)
                put("days_of_week", daysOfWeek)
                put("number_of_steps", 0)
                put("allow_ferti", if (allowFerti) 1 else 0)
                put("allow_backwash", if (allowBackwash) 1 else 0)
            }
            db.insert(TABLE_SCHEDULINGS, null, values)
        } catch (e: SQLException) {
            e.printStackTrace()
            -1
        }
    }

    fun updateScheduling(idx: Int, groupIdx: Int, name: String, hour: Int, min: Int, startTime: Int, endTime: Int?, daysOfWeek: Int, numberOfSteps: Int, allowFerti: Boolean, allowBackwash: Boolean, waterpumpIdx: Int?, waterpumpWorkingTime: Int?, fertiIdx: Int?, backwashIdx: Int?, enabled: Boolean?): Int {
        val db = this.writableDatabase
        return try {
            val values = ContentValues().apply {
                put("group_idx", groupIdx)
                put("name", name)
                put("hour", hour)
                put("min", min)
                put("start_time", startTime)
                put("end_time", endTime)
                put("days_of_week", daysOfWeek)
                put("number_of_steps", numberOfSteps)
                put("allow_ferti", if (allowFerti) 1 else 0)
                put("allow_backwash", if (allowBackwash) 1 else 0)
                put("waterpump_idx", waterpumpIdx)
                put("waterpump_working_time", waterpumpWorkingTime)
                put("ferti_idx", fertiIdx)
                put("backwash_idx", backwashIdx)
                put("enabled", enabled)
            }
            db.update(TABLE_SCHEDULINGS, values, "idx = ?", arrayOf(idx.toString()))
        } catch (e: SQLException) {
            e.printStackTrace()
            0
        }
    }

    fun updateSchedulingEnabled(idx: Int, enabled: Int): Int {
        val db = this.writableDatabase
        return try {
            val values = ContentValues().apply {
                put("enabled", enabled)
            }
            db.update(TABLE_SCHEDULINGS, values, "idx = ?", arrayOf(idx.toString()))
        } catch (e: SQLException) {
            e.printStackTrace()
            0
        }
    }

    fun getScheduling(idx: Int): Map<String, Any>? {
        val db = readableDatabase
        val query = "SELECT * FROM $TABLE_SCHEDULINGS WHERE idx = ?"
        val cursor = db.rawQuery(query, arrayOf(idx.toString()))

        return if (cursor.moveToFirst()) {
            val result = mapOf(
                "idx" to cursor.getInt(cursor.getColumnIndexOrThrow("idx")),
                "group_idx" to cursor.getInt(cursor.getColumnIndexOrThrow("group_idx")),
                "name" to cursor.getString(cursor.getColumnIndexOrThrow("name")),
                "hour" to cursor.getInt(cursor.getColumnIndexOrThrow("hour")),
                "min" to cursor.getInt(cursor.getColumnIndexOrThrow("min")),
                "days_of_week" to cursor.getInt(cursor.getColumnIndexOrThrow("days_of_week")),
                "allow_ferti" to cursor.getInt(cursor.getColumnIndexOrThrow("allow_ferti")),
                "allow_backwash" to cursor.getInt(cursor.getColumnIndexOrThrow("allow_backwash")),
                "enabled" to (cursor.getInt(cursor.getColumnIndexOrThrow("enabled")) == 1)
            )
            cursor.close()
            db.close()
            result
        } else {
            cursor.close()
            db.close()
            null
        }
    }

    fun getAllSchedulings(groupIdx: Int?): List<SchedulingItem> {
        val schedulingList = mutableListOf<SchedulingItem>()
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            cursor = if (groupIdx == null) {
                db.rawQuery("SELECT idx, group_idx, name, enabled FROM $TABLE_SCHEDULINGS", null)
            } else {
                db.rawQuery("SELECT idx, group_idx, name, enabled FROM $TABLE_SCHEDULINGS WHERE group_idx = ?", arrayOf(groupIdx.toString()))
            }

            while (cursor.moveToNext()) {
                val idx = cursor.getInt(0)
                val groupIdxValue = cursor.getInt(1)
                val name = cursor.getString(2)
                val enabled = cursor.getInt(3)==1
                schedulingList.add(SchedulingItem(idx, groupIdxValue, name, enabled))
            }

            schedulingList
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList()
        } finally {
            cursor?.close()
        }
    }

    fun getAllSchedulingsOnMap(): List<Map<String, Any>> {
        val db = this.readableDatabase
        val schedulingList = mutableListOf<Map<String, Any>>()
        var cursor: Cursor? = null

        return try {
            val query = """
            SELECT s.idx, s.ord_idx, s.name, s.start_time, s.end_time, 
                   s.number_of_steps, s.allow_ferti, s.allow_backwash, 
                   c.identity AS codec_identity
            FROM $TABLE_SCHEDULINGS s
            INNER JOIN $TABLE_GROUPS g ON s.group_idx = g.idx
            INNER JOIN $TABLE_CODECS c ON g.codec_idx = c.idx
            ORDER BY s.ord_idx ASC
        """.trimIndent()

            cursor = db.rawQuery(query, null)

            if (cursor.moveToFirst()) {
                do {
                    val item = mapOf(
                        "idx" to cursor.getInt(cursor.getColumnIndexOrThrow("idx")),
                        "ord_idx" to cursor.getInt(cursor.getColumnIndexOrThrow("ord_idx")),
                        "name" to cursor.getString(cursor.getColumnIndexOrThrow("name")),
                        "start_time" to cursor.getInt(cursor.getColumnIndexOrThrow("start_time")),
                        "end_time" to cursor.getInt(cursor.getColumnIndexOrThrow("end_time")),
                        "number_of_steps" to cursor.getInt(cursor.getColumnIndexOrThrow("number_of_steps")),
                        "allow_ferti" to cursor.getInt(cursor.getColumnIndexOrThrow("allow_ferti")),
                        "allow_backwash" to cursor.getInt(cursor.getColumnIndexOrThrow("allow_backwash")),
                        "codec_identity" to cursor.getString(cursor.getColumnIndexOrThrow("codec_identity"))
                    )
                    schedulingList.add(item)
                } while (cursor.moveToNext())
            }

            schedulingList
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    fun getAllSchedulingsByGroupOnMap(groupIdx: Int): List<Map<String, Any>> {
        val db = this.readableDatabase
        val schedulingList = mutableListOf<Map<String, Any>>()
        var cursor: Cursor? = null

        return try {
            val query = """
            SELECT idx, ord_idx, name, start_time, end_time, number_of_steps, allow_ferti, allow_backwash
            FROM $TABLE_SCHEDULINGS
            ORDER BY ord_idx ASC
        """.trimIndent()

            cursor = db.rawQuery(query, arrayOf(groupIdx.toString()))

            if (cursor.moveToFirst()) {
                do {
                    val item = mapOf(
                        "idx" to cursor.getInt(cursor.getColumnIndexOrThrow("idx")),
                        "ord_idx" to cursor.getInt(cursor.getColumnIndexOrThrow("ord_idx")),
                        "name" to cursor.getString(cursor.getColumnIndexOrThrow("name")),
                        "start_time" to cursor.getInt(cursor.getColumnIndexOrThrow("start_time")),
                        "end_time" to cursor.getInt(cursor.getColumnIndexOrThrow("end_time")),
                        "number_of_steps" to cursor.getInt(cursor.getColumnIndexOrThrow("number_of_steps")),
                        "allow_ferti" to cursor.getInt(cursor.getColumnIndexOrThrow("allow_ferti")),
                        "allow_backwash" to cursor.getInt(cursor.getColumnIndexOrThrow("allow_backwash"))
                    )
                    schedulingList.add(item)
                } while (cursor.moveToNext())
            }

            schedulingList
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    fun deleteScheduling(idx: Int): Int {
        val db = this.writableDatabase
        return try {
            db.beginTransaction()
            //db.delete(TABLE_DEVICE_SCHEDULINGS, "scheduling_idx = ?", arrayOf(idx.toString()))
            val rowsDeleted = db.delete(TABLE_SCHEDULINGS, "idx = ?", arrayOf(idx.toString()))
            db.setTransactionSuccessful()
            rowsDeleted
        } catch (e: SQLiteConstraintException) {
            e.printStackTrace()
            0
        } finally {
            db.endTransaction()
        }
    }

    fun insertSectorSchedulings(deviceSchedulings: List<Map<String, Any?>>): Boolean {
        val db = this.writableDatabase
        db.beginTransaction()
        return try {
            for (scheduling in deviceSchedulings) {
                val values = ContentValues().apply {
                    put("scheduling_idx", scheduling["scheduling_idx"] as Int)
                    put("device_idx", scheduling["device_idx"] as Int)
                    put("n_order", scheduling["n_order"] as Int)
                    put("enabled", scheduling["enabled"] as Int)
                    put("type", scheduling["type"] as Int)
                    put("ferti", scheduling["ferti"] as Int)
                    put("ferti_delay", scheduling["ferti_delay"] as Int)
                    put("working_time", scheduling["working_time"] as Int)
                }
                db.insert(TABLE_SECTOR_SCHEDULINGS, null, values)
            }
            db.setTransactionSuccessful()
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        } finally {
            db.endTransaction()
        }
    }

    fun getAllSectorSchedulings(schedulingIdx: Int): MutableList<SectorSchedulingItem> {
        val sectorSchedulingList = mutableListOf<SectorSchedulingItem>()
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            val query = """
        SELECT ss.device_idx, ss.type, ss.n_order, ss.enabled, d.sector, ss.working_time, ss.ferti, ss.ferti_delay
        FROM $TABLE_SECTOR_SCHEDULINGS ss
        JOIN $TABLE_DEVICES d ON ss.device_idx = d.idx
        WHERE ss.scheduling_idx = ? AND ss.type = 0
        ORDER BY ss.n_order ASC""".trimIndent()

            cursor = db.rawQuery(query, arrayOf(schedulingIdx.toString()))

            while (cursor.moveToNext()) {
                val deviceIdx = cursor.getInt(cursor.getColumnIndexOrThrow("device_idx"))
                val type = cursor.getInt(cursor.getColumnIndexOrThrow("type"))
                val lastNOrder = cursor.getInt(cursor.getColumnIndexOrThrow("n_order"))
                val enabled = cursor.getInt(cursor.getColumnIndexOrThrow("enabled")) == 1
                val sector = cursor.getString(cursor.getColumnIndexOrThrow("sector")) ?: ""
                val duration = cursor.getString(cursor.getColumnIndexOrThrow("working_time"))
                val ferti = cursor.getString(cursor.getColumnIndexOrThrow("ferti"))
                val ferti_delay = cursor.getString(cursor.getColumnIndexOrThrow("ferti_delay"))

                sectorSchedulingList.add(SectorSchedulingItem(deviceIdx, type, lastNOrder, enabled, sector, duration, ferti, ferti_delay))
            }

            sectorSchedulingList
        } catch (e: SQLException) {
            e.printStackTrace()
            mutableListOf()
        } finally {
            cursor?.close()
        }
    }

    fun deleteSectorSchedulings(idx: Int): Int {
        val db = this.writableDatabase
        return try {
            db.delete(TABLE_SECTOR_SCHEDULINGS, "scheduling_idx = ?", arrayOf(idx.toString()))
        } catch (e: SQLException) {
            e.printStackTrace()
            0
        }
    }

    fun insertDeviceSchedulings(deviceSchedulings: List<Map<String, Any?>>): Boolean {
        val db = this.writableDatabase
        db.beginTransaction()
        return try {
            for (scheduling in deviceSchedulings) {
                val values = ContentValues().apply {
                    put("ord_idx", 0)
                    put("scheduling_idx", scheduling["scheduling_idx"] as Int)
                    put("device_idx", scheduling["device_idx"] as Int)
                    put("n_order", scheduling["n_order"] as Int)
                    put("status", scheduling["status"] as Int)
                    put("type", scheduling["type"] as Int)
                    put("time", scheduling["time"] as Int)
                    put("sector_working_time", scheduling["sector_working_time"] as Int)
                    put("ferti_working_time", scheduling["ferti_working_time"] as Int)
                    put("ferti_delay", scheduling["ferti_delay"] as Int)
                }
                db.insert(TABLE_DEVICE_SCHEDULINGS, null, values)
            }
            db.setTransactionSuccessful()
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        } finally {
            db.endTransaction()
        }
    }

    fun getAllDeviceSchedulings(schedulingIdx: Int): MutableList<SectorSchedulingItem> {
        val sectorSchedulingList = mutableListOf<SectorSchedulingItem>()
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            val query = """
        SELECT ds.device_idx, ds.type, ds.n_order, ds.status, d.sector, ds.working_time
        FROM $TABLE_DEVICE_SCHEDULINGS ds
        JOIN $TABLE_DEVICES d ON ds.device_idx = d.idx
        WHERE ds.scheduling_idx = ? AND ds.type = 0
        ORDER BY ds.n_order ASC""".trimIndent()

            cursor = db.rawQuery(query, arrayOf(schedulingIdx.toString()))

            while (cursor.moveToNext()) {
                val deviceIdx = cursor.getInt(cursor.getColumnIndexOrThrow("device_idx"))
                val type = cursor.getInt(cursor.getColumnIndexOrThrow("type"))
                val lastNOrder = cursor.getInt(cursor.getColumnIndexOrThrow("n_order"))
                val enabled = cursor.getInt(cursor.getColumnIndexOrThrow("status")) == 1
                val sector = cursor.getString(cursor.getColumnIndexOrThrow("sector")) ?: ""
                val duration = cursor.getString(cursor.getColumnIndexOrThrow("working_time"))

                sectorSchedulingList.add(SectorSchedulingItem(deviceIdx, type, lastNOrder, enabled, sector, duration, "0", "0"))
            }

            sectorSchedulingList
        } catch (e: SQLException) {
            e.printStackTrace()
            mutableListOf()
        } finally {
            cursor?.close()
        }
    }

    fun getAllDeviceSchedulingsOnMap(schedulingIdx: Int?): List<Map<String, Any>> {
        val deviceSchedulingList = mutableListOf<Map<String, Any>>()
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            cursor = if (schedulingIdx == null) {
                db.rawQuery("SELECT * FROM $TABLE_DEVICE_SCHEDULINGS", null)
            } else {
                db.rawQuery("SELECT * FROM $TABLE_DEVICE_SCHEDULINGS WHERE scheduling_idx = ?", arrayOf(schedulingIdx.toString()))
            }

            while (cursor.moveToNext()) {
                val item = mutableMapOf<String, Any>()
                item["idx"] = cursor.getInt(cursor.getColumnIndexOrThrow("idx"))
                item["scheduling_idx"] = cursor.getInt(cursor.getColumnIndexOrThrow("scheduling_idx"))
                item["codec_idx"] = cursor.getInt(cursor.getColumnIndexOrThrow("codec_idx"))
                item["mesh_idx"] = cursor.getInt(cursor.getColumnIndexOrThrow("mesh_idx"))
                item["device_idx"] = cursor.getInt(cursor.getColumnIndexOrThrow("device_idx"))
                item["type"] = cursor.getInt(cursor.getColumnIndexOrThrow("type"))
                item["time"] = cursor.getInt(cursor.getColumnIndexOrThrow("time"))
                item["working_time"] = cursor.getInt(cursor.getColumnIndexOrThrow("working_time"))
                deviceSchedulingList.add(item)
            }

            deviceSchedulingList
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList()
        } finally {
            cursor?.close()
        }
    }

    fun deleteDeviceSchedulings(idx: Int): Int {
        val db = this.writableDatabase
        return try {
            db.delete(TABLE_DEVICE_SCHEDULINGS, "scheduling_idx = ?", arrayOf(idx.toString()))
        } catch (e: SQLException) {
            e.printStackTrace()
            0
        }
    }

    fun insertMeshDevice(identity: String, name: String, type: Int, mode: Int, equipament: Int, checkInput: Int, devicesBitmask: Int, levelPumpIdx: Int?, levelPumpEnable: Int?, levelPumpWorkingTime: Int?, codecIdx: Int, groupIdx: Int): Long {
        val db = this.writableDatabase
        return try {
            val values = ContentValues().apply {
                put("identity", identity)
                put("name", name)
                put("type", type)
                put("mode", mode)
                put("equipament", equipament)
                put("check_input", checkInput)
                put("devices_bitmask", devicesBitmask)
                put("level_pump_idx", levelPumpIdx)
                put("level_pump_enable", levelPumpEnable)
                put("level_pump_working_time", levelPumpWorkingTime)
                put("codec_idx", codecIdx)
                put("group_idx", groupIdx)
            }
            db.insert(TABLE_MESH_DEVICES, null, values)
        } catch (e: SQLException) {
            e.printStackTrace()
            -1
        }
    }

    fun updateMeshDevice(idx: Int, identity: String, name: String, type: Int, mode: Int, equipament: Int, checkInput: Int, devicesBitmask: Int, levelPumpIdx: Int?, levelPumpEnable: Int?, levelPumpWorkingTime: Int?, codecIdx: Int, groupIdx: Int): Int {
        val db = this.writableDatabase
        return try {
            val values = ContentValues().apply {
                put("identity", identity)
                put("name", name)
                put("type", type)
                put("mode", mode)
                put("equipament", equipament)
                put("check_input", checkInput)
                put("devices_bitmask", devicesBitmask)
                put("level_pump_idx", levelPumpIdx)
                put("level_pump_enable", levelPumpEnable)
                put("level_pump_working_time", levelPumpWorkingTime)
                put("codec_idx", codecIdx)
                put("group_idx", groupIdx)
            }
            db.update(TABLE_MESH_DEVICES, values, "idx = ?", arrayOf(idx.toString()))
        } catch (e: SQLException) {
            e.printStackTrace()
            0
        }
    }

    fun getAllMeshDevices(groupIdx: Int): List<MeshDeviceItem> {
        val meshDeviceList = mutableListOf<MeshDeviceItem>()
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            cursor = db.rawQuery("SELECT idx, identity, name, type FROM $TABLE_MESH_DEVICES WHERE group_idx = ?", arrayOf(groupIdx.toString()))

            if (cursor.moveToFirst()) {
                do {
                    val idx = cursor.getInt(0)
                    val identity = "%06X".format(cursor.getString(1).toInt())
                    val name = cursor.getString(2)
                    val type = cursor.getInt(3)

                    val strType = when (type) {
                        0 -> "Valvulas"
                        1 -> "Bomba"
                        2 -> "Controle de Nível"
                        else -> ""
                    }
                    val label = "ID: $identity - $name"
                    meshDeviceList.add(MeshDeviceItem(idx, identity, name, label))
                } while (cursor.moveToNext())
            }

            meshDeviceList
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList()
        } finally {
            cursor?.close()
        }
    }

    fun getMeshDeviceById(idx: Int): Map<String, Any>? {
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            cursor = db.rawQuery(
                "SELECT identity, name, type, mode, equipament, check_input, devices_bitmask, level_pump_idx, level_pump_working_time FROM $TABLE_MESH_DEVICES WHERE idx = ?",
                arrayOf(idx.toString())
            )

            if (cursor.moveToFirst()) {
                mapOf(
                    "identity" to cursor.getString(0),
                    "name" to cursor.getString(1),
                    "type" to cursor.getInt(2),
                    "mode" to cursor.getInt(3),
                    "equipament" to cursor.getInt(4),
                    "check_input" to cursor.getInt(5),
                    "devices_bitmask" to cursor.getInt(6),
                    "level_pump_idx" to cursor.getInt(7),
                    "level_pump_working_time" to cursor.getInt(8)
                )
            } else {
                null
            }
        } catch (e: SQLException) {
            e.printStackTrace()
            null
        } finally {
            cursor?.close()
        }
    }


    fun deleteMeshDevice(idx: Int): Int {
        val db = this.writableDatabase
        return try {
            /*val sql = "DELETE FROM $TABLE_DEVICES WHERE mesh_idx = ?"
            val statement = db.compileStatement(sql)
            statement.bindLong(1, idx.toLong())
            statement.executeUpdateDelete()*/
            db.delete(TABLE_MESH_DEVICES, "idx = ?", arrayOf(idx.toString()))
        } catch (e: SQLException) {
            e.printStackTrace()
            0
        }
    }

    fun insertDevice(meshIdx: Int, identity: String, type: String, out1: String?, out2: String?, input: String?, mode: String?, sector: String?): Long {
        val db = this.writableDatabase
        return try {
            val values = ContentValues().apply {
                put("mesh_id", meshIdx)
                put("identity", identity)
                put("type", type)
                put("out1", out1)
                put("out2", out2)
                put("input", input)
                put("mode", mode)
                put("sector", sector)
            }
            db.insert(TABLE_DEVICES, null, values)
        } catch (e: SQLException) {
            e.printStackTrace()
            -1
        }
    }

    private data class DeviceRow(val idx: Int, val identity: String)

    fun syncDevicesForMesh(meshIdx: Int, desired: List<Map<String, Any?>>): Boolean {
        val db = writableDatabase
        db.beginTransaction()
        try {
            fun buildCV(spec: Map<String, Any?>): ContentValues = ContentValues().apply {
                put("mesh_idx", meshIdx)
                put("identity", spec["identity"]?.toString())
                put("type",     (spec["type"]   as? Int) ?: 0)
                put("out1",     spec["out1"]?.toString())
                put("out2",     spec["out2"]?.toString())
                put("input",    spec["input"]?.toString())
                put("mode",     (spec["mode"]   as? Int) ?: 0)
                put("sector",   spec["sector"]?.toString())
                put("ord_idx",  (spec["ord_idx"] as? Int) ?: 0)
            }

            val existing = mutableMapOf<String, DeviceRow>()
            db.rawQuery(
                "SELECT idx, identity FROM $TABLE_DEVICES WHERE mesh_idx = ?",
                arrayOf(meshIdx.toString())
            ).use { c ->
                while (c.moveToNext()) {
                    existing[c.getString(1)] = DeviceRow(c.getInt(0), c.getString(1))
                }
            }

            // update ou insert
            for (spec in desired) {
                val identity = spec["identity"]?.toString() ?: continue
                val cv = buildCV(spec)

                val row = existing.remove(identity)
                if (row == null) {
                    db.insert(TABLE_DEVICES, null, cv)
                } else {
                    db.update(TABLE_DEVICES, cv, "idx = ?", arrayOf(row.idx.toString()))
                }
            }

            // delete dos que sobraram
            for (row in existing.values) {
                db.delete(TABLE_DEVICES, "idx = ?", arrayOf(row.idx.toString()))
            }

            db.setTransactionSuccessful()
            return true
        } catch (e: Exception) {
            e.printStackTrace()
            return false
        } finally {
            db.endTransaction()
        }
    }

    fun insertDevices(devices: List<Map<String, Any?>>): Boolean {
        val db = this.writableDatabase
        db.beginTransaction()
        return try {
            for (device in devices) {
                val values = ContentValues().apply {
                    put("ord_idx", 0)
                    put("mesh_idx", device["mesh_idx"] as Int)
                    put("identity", device["identity"] as String)
                    put("type", device["type"] as Int)
                    put("out1", device["out1"] as String?)
                    put("out2", device["out2"] as String?)
                    put("input", device["input"] as String?)
                    put("mode", device["mode"] as Int)
                    put("sector", device["sector"] as String?)
                }
                db.insert(TABLE_DEVICES, null, values)
            }
            db.setTransactionSuccessful()
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        } finally {
            db.endTransaction()
        }
    }

    fun deleteDevicesByMeshId(meshIdx: Int): Int {
        val db = this.writableDatabase

        return try {
            db.delete(TABLE_DEVICES, "mesh_idx = ?", arrayOf(meshIdx.toString()))
        } catch (e: SQLException) {
            e.printStackTrace()
            0
        } finally {
            db.close()
        }
    }


    fun getAllDevicesByGroup(groupIdx: Int): List<Map<String, Any>> {
        val deviceList = mutableListOf<Map<String, Any>>()
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            cursor = db.rawQuery(
                """
            SELECT d.idx, d.mesh_idx, d.identity, d.type, d.out1, d.out2, d.input, d.mode, d.sector 
            FROM $TABLE_DEVICES d
            JOIN $TABLE_MESH_DEVICES m ON d.mesh_idx = m.idx
            WHERE m.group_idx = ?
            ORDER BY d.sector ASC
            """.trimIndent(),
                arrayOf(groupIdx.toString())
            )

            while (cursor.moveToNext()) {
                val item = mutableMapOf<String, Any>()
                item["idx"] = cursor.getInt(cursor.getColumnIndexOrThrow("idx"))
                item["mesh_idx"] = cursor.getInt(cursor.getColumnIndexOrThrow("mesh_idx"))
                item["identity"] = cursor.getString(cursor.getColumnIndexOrThrow("identity"))
                item["type"] = cursor.getString(cursor.getColumnIndexOrThrow("type"))
                item["out1"] = cursor.getString(cursor.getColumnIndexOrThrow("out1")) ?: ""
                item["out2"] = cursor.getString(cursor.getColumnIndexOrThrow("out2")) ?: ""
                item["input"] = cursor.getString(cursor.getColumnIndexOrThrow("input")) ?: ""
                item["mode"] = cursor.getString(cursor.getColumnIndexOrThrow("mode")) ?: ""
                item["sector"] = cursor.getString(cursor.getColumnIndexOrThrow("sector")) ?: ""

                deviceList.add(item)
            }

            deviceList
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    fun getAllDevicesByMesh(meshIdx: Int): List<Map<String, Any>> {
        val deviceList = mutableListOf<Map<String, Any>>()
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            cursor = db.rawQuery(
                """
            SELECT idx, mesh_idx, identity, type, out1, out2, input, mode, sector
            FROM $TABLE_DEVICES
            WHERE mesh_idx = ?
            ORDER BY sector ASC
            """.trimIndent(),
                arrayOf(meshIdx.toString())
            )

            while (cursor.moveToNext()) {
                val item = mutableMapOf<String, Any>()
                item["idx"] = cursor.getInt(cursor.getColumnIndexOrThrow("idx"))
                item["mesh_idx"] = cursor.getInt(cursor.getColumnIndexOrThrow("mesh_idx"))
                item["identity"] = cursor.getString(cursor.getColumnIndexOrThrow("identity"))
                item["type"] = cursor.getInt(cursor.getColumnIndexOrThrow("type"))
                item["out1"] = cursor.getInt(cursor.getColumnIndexOrThrow("out1"))
                item["out2"] = cursor.getInt(cursor.getColumnIndexOrThrow("out2"))
                item["input"] = cursor.getInt(cursor.getColumnIndexOrThrow("input"))
                item["mode"] = cursor.getInt(cursor.getColumnIndexOrThrow("mode"))
                item["sector"] = cursor.getInt(cursor.getColumnIndexOrThrow("sector"))

                deviceList.add(item)
            }

            deviceList
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    fun getAllDevicesByType(type: Int): List<DeviceItem> {
        val deviceList = mutableListOf<DeviceItem>()
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            cursor = db.rawQuery(
                """
            SELECT d.idx, d.ord_idx, d.mesh_idx, d.identity AS device_identity, d.type, d.out1, d.out2, d.input, d.mode, d.sector, 
                   m.name AS mesh_name, m.identity AS mesh_identity
            FROM $TABLE_DEVICES d
            JOIN $TABLE_MESH_DEVICES m ON d.mesh_idx = m.idx
            WHERE d.type = ? 
            ORDER BY d.idx ASC
            """.trimIndent(),
                arrayOf(type.toString())
            )

            while (cursor.moveToNext()) {
                val idx = cursor.getInt(cursor.getColumnIndexOrThrow("idx"))
                val ord_idx = cursor.getInt(cursor.getColumnIndexOrThrow("ord_idx"))
                val name = cursor.getString(cursor.getColumnIndexOrThrow("mesh_name"))
                val type = cursor.getInt(cursor.getColumnIndexOrThrow("type"))
                val deviceIdentity = cursor.getString(cursor.getColumnIndexOrThrow("device_identity"))
                val meshIdentity = cursor.getString(cursor.getColumnIndexOrThrow("mesh_identity"))
                deviceList.add(DeviceItem(idx, ord_idx, name, type, deviceIdentity, meshIdentity, "", null, null))
            }

            deviceList
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    fun getAllDevicesByTypeOnMap(type: Int): List<Map<String, Any?>> {
        val deviceList = mutableListOf<Map<String, Any?>>()
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            cursor = db.rawQuery(
                """
            SELECT d.idx, d.mesh_idx, d.identity, d.type, d.out1, d.out2, d.input, d.mode, d.sector, 
                   m.name AS mesh_name
            FROM $TABLE_DEVICES d
            JOIN $TABLE_MESH_DEVICES m ON d.mesh_idx = m.idx
            WHERE d.type = ? 
            ORDER BY d.idx ASC
            """.trimIndent(),
                arrayOf(type.toString())
            )

            while (cursor.moveToNext()) {
                val idx = cursor.getInt(cursor.getColumnIndexOrThrow("idx"))
                val meshIdx = cursor.getInt(cursor.getColumnIndexOrThrow("mesh_idx"))
                val identity = cursor.getString(cursor.getColumnIndexOrThrow("identity"))
                val deviceType = cursor.getInt(cursor.getColumnIndexOrThrow("type"))
                val out1 = cursor.getInt(cursor.getColumnIndexOrThrow("out1"))
                val out2 = cursor.getInt(cursor.getColumnIndexOrThrow("out2"))
                val input = cursor.getInt(cursor.getColumnIndexOrThrow("input"))
                val mode = cursor.getInt(cursor.getColumnIndexOrThrow("mode"))
                val sector = cursor.getInt(cursor.getColumnIndexOrThrow("sector"))
                val meshName = cursor.getString(cursor.getColumnIndexOrThrow("mesh_name"))

                val deviceMap = mapOf(
                    "idx" to idx,
                    "mesh_idx" to meshIdx,
                    "identity" to identity,
                    "type" to deviceType,
                    "out1" to out1,
                    "out2" to out2,
                    "input" to input,
                    "mode" to mode,
                    "sector" to sector,
                    "mesh_name" to meshName
                )

                deviceList.add(deviceMap)
            }

            deviceList
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    fun getAllDevicesWithMeshInfo(): List<DeviceItem> {
        val deviceList = mutableListOf<DeviceItem>()
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            val query = """
            SELECT 
                d.idx AS idx, 
                d.ord_idx AS ord_idx, 
                d.type AS device_type, 
                d.identity AS device_identity,
                m.identity AS mesh_identity, 
                m.name AS mesh_name,
                c.identity AS codec_identity
            FROM $TABLE_DEVICES d
            JOIN $TABLE_MESH_DEVICES m ON d.mesh_idx = m.idx
            LEFT JOIN $TABLE_CODECS c ON m.codec_idx = c.idx
        """.trimIndent()

            cursor = db.rawQuery(query, null)

            while (cursor.moveToNext()) {
                val idx = cursor.getInt(cursor.getColumnIndexOrThrow("idx"))
                val ordIdx = cursor.getInt(cursor.getColumnIndexOrThrow("ord_idx"))
                val name = cursor.getString(cursor.getColumnIndexOrThrow("mesh_name"))
                val type = cursor.getInt(cursor.getColumnIndexOrThrow("device_type"))
                val deviceIdentity = cursor.getString(cursor.getColumnIndexOrThrow("device_identity"))
                val meshIdentity = "%06X".format(cursor.getString(cursor.getColumnIndexOrThrow("mesh_identity")).toInt())
                val codecIdentity = cursor.getString(cursor.getColumnIndexOrThrow("codec_identity"))
                deviceList.add(DeviceItem(idx, ordIdx, name, type, deviceIdentity, meshIdentity, codecIdentity, null, null))
            }

            deviceList
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    fun getMaxSector(): Int {
        val db = this.readableDatabase
        var cursor: Cursor? = null
        var maxSector = 0

        return try {
            cursor = db.rawQuery("SELECT MAX(sector) FROM $TABLE_DEVICES", null)

            if (cursor.moveToFirst() && !cursor.isNull(0)) {
                maxSector = cursor.getInt(0)
            }

            maxSector
        } catch (e: SQLException) {
            e.printStackTrace()
            0
        } finally {
            cursor?.close()
            db.close()
        }
    }


    fun getDevicesByCodec(codecIdx: Int): List<Map<String, Any>> {
        val db = this.readableDatabase
        val deviceList = mutableListOf<Map<String, Any>>()
        var cursor: Cursor? = null

        return try {
            val query = """
            SELECT d.idx, d.ord_idx, d.mesh_idx, d.identity, d.type, d.out1, d.out2, d.input, d.mode, d.sector, 
                   m.identity AS mesh_id, m.group_idx
            FROM $TABLE_DEVICES d
            JOIN $TABLE_MESH_DEVICES m ON d.mesh_idx = m.idx
            WHERE m.codec_idx = ? 
            ORDER BY d.idx ASC
        """.trimIndent()

            cursor = db.rawQuery(query, arrayOf(codecIdx.toString()))

            if (cursor.moveToFirst()) {
                do {
                    val item = mutableMapOf<String, Any>()
                    item["ix"] = cursor.getInt(cursor.getColumnIndexOrThrow("ord_idx"))
                    item["mi"] = cursor.getInt(cursor.getColumnIndexOrThrow("mesh_id"))
                    item["di"] = cursor.getString(cursor.getColumnIndexOrThrow("identity")).toIntOrNull() ?: 0
                    item["tp"] = cursor.getInt(cursor.getColumnIndexOrThrow("type"))
                    item["o1"] = cursor.getInt(cursor.getColumnIndexOrThrow("out1"))
                    item["o2"] = cursor.getInt(cursor.getColumnIndexOrThrow("out2"))
                    item["ip"] = cursor.getInt(cursor.getColumnIndexOrThrow("input"))
                    item["md"] = cursor.getInt(cursor.getColumnIndexOrThrow("mode"))
                    item["sc"] = cursor.getInt(cursor.getColumnIndexOrThrow("sector"))
                    item["gp"] = cursor.getInt(cursor.getColumnIndexOrThrow("group_idx"))
                    item["eq"] = 0

                    deviceList.add(item)
                } while (cursor.moveToNext())
            }
            deviceList
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    fun getDeviceByIdx(idx: Int): Map<String, Any>? {
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            val query = """
            SELECT d.ord_idx, d.type, c.identity AS codec_identity
            FROM $TABLE_DEVICES d
            JOIN $TABLE_MESH_DEVICES m ON d.mesh_idx = m.idx
            JOIN $TABLE_GROUPS g ON m.group_idx = g.idx
            JOIN $TABLE_CODECS c ON g.codec_idx = c.idx
            WHERE d.idx = ?
        """.trimIndent()

            cursor = db.rawQuery(query, arrayOf(idx.toString()))

            if (cursor.moveToFirst()) {
                mapOf(
                    "ord_idx" to cursor.getInt(cursor.getColumnIndexOrThrow("ord_idx")),
                    "type" to cursor.getInt(cursor.getColumnIndexOrThrow("type")),
                    "codec_identity" to cursor.getString(cursor.getColumnIndexOrThrow("codec_identity"))
                )
            } else {
                null
            }
        } catch (e: SQLException) {
            e.printStackTrace()
            null
        } finally {
            cursor?.close()
            db.close()
        }
    }

    fun devicesReorganizeOrdIdxByCodec(codecIdx: Int) {
        val db = this.writableDatabase
        var cursor: Cursor? = null
        try {
            cursor = db.rawQuery(
                """
            SELECT D.idx 
            FROM $TABLE_DEVICES D
            INNER JOIN $TABLE_MESH_DEVICES M ON D.mesh_idx = M.idx
            WHERE M.codec_idx = ?
            ORDER BY D.idx ASC
            """, arrayOf(codecIdx.toString())
            )

            var ordIdx = 0
            while (cursor.moveToNext()) {
                val idx = cursor.getInt(cursor.getColumnIndexOrThrow("idx"))

                val values = ContentValues().apply {
                    put("ord_idx", ordIdx)
                }
                db.update(TABLE_DEVICES, values, "idx = ?", arrayOf(idx.toString()))

                ordIdx++
            }
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    fun getSchedulingsByCodec(codecIdx: Int, nicknames: Boolean = false): MutableList<MutableMap<String, Any>> {
        val db = this.readableDatabase
        val schedulingList = mutableListOf<MutableMap<String, Any>>()
        var cursor: Cursor? = null

        return try {
            val query = """
            SELECT DISTINCT s.*
            FROM $TABLE_SCHEDULINGS s
            JOIN $TABLE_GROUPS g ON s.group_idx = g.idx
            JOIN $TABLE_MESH_DEVICES m ON g.idx = m.group_idx
            WHERE m.codec_idx = ? AND s.enabled = 1
            ORDER BY s.idx ASC
        """.trimIndent()

            cursor = db.rawQuery(query, arrayOf(codecIdx.toString()))

            if(nicknames) {
                if (cursor.moveToFirst()) {
                    do {
                        val item = mutableMapOf<String, Any>()
                        item["ix"] = cursor.getInt(cursor.getColumnIndexOrThrow("ord_idx"))
                        //item["ix"] = cursor.getInt(cursor.getColumnIndexOrThrow("idx"))
                        item["gi"] = cursor.getInt(cursor.getColumnIndexOrThrow("group_idx"))
                        //item["nm"] = cursor.getInt(cursor.getColumnIndexOrThrow("name"))
                        //item["hr"] = cursor.getInt(cursor.getColumnIndexOrThrow("hour"))
                        //item["mn"] = cursor.getInt(cursor.getColumnIndexOrThrow("min"))
                        item["st"] = cursor.getInt(cursor.getColumnIndexOrThrow("start_time"))
                        item["dw"] = cursor.getInt(cursor.getColumnIndexOrThrow("days_of_week"))
                        item["ns"] = cursor.getInt(cursor.getColumnIndexOrThrow("number_of_steps"))
                        item["af"] = cursor.getInt(cursor.getColumnIndexOrThrow("allow_ferti"))
                        item["ab"] = cursor.getInt(cursor.getColumnIndexOrThrow("allow_backwash"))
                        item["wi"] = cursor.getInt(cursor.getColumnIndexOrThrow("waterpump_ord_idx"))
                        item["wt"] = cursor.getInt(cursor.getColumnIndexOrThrow("waterpump_working_time"))
                        item["fi"] = cursor.getInt(cursor.getColumnIndexOrThrow("ferti_ord_idx"))
                        item["bi"] = cursor.getInt(cursor.getColumnIndexOrThrow("backwash_ord_idx"))
                        schedulingList.add(item)
                    } while (cursor.moveToNext())
                }
            }else{
                while (cursor.moveToNext()) {
                    val item = mutableMapOf<String, Any>()
                    for (i in 0 until cursor.columnCount) {
                        val columnName = cursor.getColumnName(i)
                        when (cursor.getType(i)) {
                            Cursor.FIELD_TYPE_INTEGER -> item[columnName] = cursor.getInt(i)
                            Cursor.FIELD_TYPE_FLOAT -> item[columnName] = cursor.getFloat(i)
                            Cursor.FIELD_TYPE_STRING -> item[columnName] = cursor.getString(i)
                            Cursor.FIELD_TYPE_BLOB -> item[columnName] = cursor.getBlob(i)
                            Cursor.FIELD_TYPE_NULL -> item[columnName] = "NULL"
                        }
                    }
                    schedulingList.add(item)
                }
            }

            schedulingList
        } catch (e: SQLException) {
            e.printStackTrace()
            mutableListOf()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    fun getDevicesSchedulingsByScheduling(schedulingIdx: Int): List<Map<String, Any>> {
        val db = this.readableDatabase
        val deviceSchedulingList = mutableListOf<Map<String, Any>>()
        var cursor: Cursor? = null

        return try {
            val query = """
        SELECT ds.idx, ds.ord_idx, d.ord_idx AS device_ord_idx, ds.scheduling_idx, ds.device_idx, ds.n_order, ds.status, ds.type, ds.time, ds.sector_working_time,
               ds.ferti_working_time, s.days_of_week, s.ord_idx AS scheduling_ord_idx
        FROM $TABLE_DEVICE_SCHEDULINGS ds
        JOIN $TABLE_SCHEDULINGS s ON ds.scheduling_idx = s.idx
        JOIN $TABLE_GROUPS g ON s.group_idx = g.idx
        JOIN $TABLE_MESH_DEVICES m ON g.idx = m.group_idx
        JOIN $TABLE_DEVICES d ON ds.device_idx = d.idx 
        WHERE ds.scheduling_idx = ?
        GROUP BY ds.idx 
        ORDER BY ds.idx ASC
    """.trimIndent()

            cursor = db.rawQuery(query, arrayOf(schedulingIdx.toString()))

            if (cursor.moveToFirst()) {
                do {
                    val item = mutableMapOf<String, Any>()
                    item["ix"] = cursor.getInt(cursor.getColumnIndexOrThrow("ord_idx"))
                    item["sh"] = cursor.getInt(cursor.getColumnIndexOrThrow("scheduling_ord_idx"))
                    item["dx"] = cursor.getInt(cursor.getColumnIndexOrThrow("device_ord_idx"))
                    item["od"] = cursor.getInt(cursor.getColumnIndexOrThrow("n_order"))
                    item["st"] = cursor.getInt(cursor.getColumnIndexOrThrow("status"))
                    item["tp"] = cursor.getInt(cursor.getColumnIndexOrThrow("type"))
                    item["tm"] = cursor.getInt(cursor.getColumnIndexOrThrow("time"))
                    item["st"] = cursor.getInt(cursor.getColumnIndexOrThrow("sector_working_time"))
                    item["ft"] = cursor.getInt(cursor.getColumnIndexOrThrow("ferti_working_time"))

                    deviceSchedulingList.add(item)
                } while (cursor.moveToNext())
            }

            deviceSchedulingList
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    fun getDevicesSchedulingsByCodec(codecIdx: Int): List<Map<String, Any>> {
        val db = this.readableDatabase
        val schedulingList = mutableListOf<Map<String, Any>>()
        var cursor: Cursor? = null

        return try {
            val query = """
            SELECT ds.idx, ds.ord_idx, d.ord_idx AS device_ord_idx, ds.scheduling_idx, ds.device_idx, ds.n_order, ds.status, ds.type, ds.time, ds.sector_working_time,
                   ds.ferti_working_time, ds.ferti_delay, s.days_of_week, s.ord_idx AS scheduling_ord_idx
            FROM $TABLE_DEVICE_SCHEDULINGS ds
            JOIN $TABLE_SCHEDULINGS s ON ds.scheduling_idx = s.idx
            JOIN $TABLE_GROUPS g ON s.group_idx = g.idx
            JOIN $TABLE_MESH_DEVICES m ON g.idx = m.group_idx
            JOIN $TABLE_DEVICES d ON ds.device_idx = d.idx 
            WHERE m.codec_idx = ? AND s.enabled = 1 
            GROUP BY ds.idx 
            ORDER BY ds.idx ASC
        """.trimIndent()

            cursor = db.rawQuery(query, arrayOf(codecIdx.toString()))

            if (cursor.moveToFirst()) {
                do {
                    val item = mutableMapOf<String, Any>()
                    item["ix"] = cursor.getInt(cursor.getColumnIndexOrThrow("ord_idx"))
                    item["sh"] = cursor.getInt(cursor.getColumnIndexOrThrow("scheduling_ord_idx"))
                    item["dx"] = cursor.getInt(cursor.getColumnIndexOrThrow("device_ord_idx"))
                    item["od"] = cursor.getInt(cursor.getColumnIndexOrThrow("n_order"))
                    item["tp"] = cursor.getInt(cursor.getColumnIndexOrThrow("type"))
                    item["tm"] = cursor.getInt(cursor.getColumnIndexOrThrow("time"))
                    item["st"] = cursor.getInt(cursor.getColumnIndexOrThrow("sector_working_time"))
                    item["ft"] = cursor.getInt(cursor.getColumnIndexOrThrow("ferti_working_time"))
                    item["fd"] = cursor.getInt(cursor.getColumnIndexOrThrow("ferti_delay"))
                    item["dw"] = cursor.getInt(cursor.getColumnIndexOrThrow("days_of_week"))

                    schedulingList.add(item)
                } while (cursor.moveToNext())
            }

            schedulingList
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    fun getAllDeviceSchedulingsOnMap(): List<Map<String, Any>> {
        val db = this.readableDatabase
        val list = mutableListOf<Map<String, Any>>()
        var cursor: Cursor? = null

        return try {
            val query = """
            SELECT ds.idx,
                   ds.ord_idx,
                   ds.scheduling_idx,
                   ds.device_idx,
                   ds.n_order,
                   ds.status,
                   ds.type,
                   ds.time,
                   ds.sector_working_time,
                   ds.ferti_working_time,
                   ds.ferti_delay,
                   d.sector,
                   s.ord_idx AS scheduling_ord_idx,
                   c.identity AS codec_identity
            FROM $TABLE_DEVICE_SCHEDULINGS  ds
            INNER JOIN $TABLE_DEVICES       d  ON ds.device_idx     = d.idx
            INNER JOIN $TABLE_SCHEDULINGS   s  ON ds.scheduling_idx = s.idx
            INNER JOIN $TABLE_GROUPS        g  ON s.group_idx       = g.idx
            INNER JOIN $TABLE_CODECS        c  ON g.codec_idx       = c.idx
            ORDER BY s.ord_idx ASC, ds.ord_idx ASC
        """.trimIndent()

            cursor = db.rawQuery(query, null)

            if (cursor.moveToFirst()) {
                do {
                    val item = mapOf(
                        "idx"                    to cursor.getInt   (cursor.getColumnIndexOrThrow("idx")),
                        "ord_idx"                to cursor.getInt   (cursor.getColumnIndexOrThrow("ord_idx")),
                        "scheduling_idx"         to cursor.getInt   (cursor.getColumnIndexOrThrow("scheduling_idx")),
                        "device_idx"             to cursor.getInt   (cursor.getColumnIndexOrThrow("device_idx")),
                        "n_order"                to cursor.getInt   (cursor.getColumnIndexOrThrow("n_order")),
                        "status"                 to cursor.getInt   (cursor.getColumnIndexOrThrow("status")),
                        "type"                   to cursor.getInt   (cursor.getColumnIndexOrThrow("type")),
                        "time"                   to cursor.getInt   (cursor.getColumnIndexOrThrow("time")),
                        "sector_working_time"    to cursor.getInt   (cursor.getColumnIndexOrThrow("sector_working_time")),
                        "ferti_working_time"     to cursor.getInt   (cursor.getColumnIndexOrThrow("ferti_working_time")),
                        "ferti_delay"            to cursor.getInt   (cursor.getColumnIndexOrThrow("ferti_delay")),
                        "sector"                 to cursor.getInt   (cursor.getColumnIndexOrThrow("sector")),
                        "scheduling_ord_idx"     to cursor.getInt   (cursor.getColumnIndexOrThrow("scheduling_ord_idx")),
                        "codec_identity"         to cursor.getString(cursor.getColumnIndexOrThrow("codec_identity"))
                    )
                    list.add(item)
                } while (cursor.moveToNext())
            }

            list
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    fun schedulingsReorganizeOrdIdx() {
        val db = this.writableDatabase
        var cursor: Cursor? = null
        try {
            cursor = db.rawQuery("SELECT idx, waterpump_idx, ferti_idx, backwash_idx FROM $TABLE_SCHEDULINGS ORDER BY idx ASC", null)

            var ordIdx = 0
            while (cursor.moveToNext()) {
                val idx = cursor.getInt(cursor.getColumnIndexOrThrow("idx"))
                val waterpumpIdx = cursor.getIntOrNull(cursor.getColumnIndexOrThrow("waterpump_idx"))
                val fertiIdx = cursor.getIntOrNull(cursor.getColumnIndexOrThrow("ferti_idx"))
                val backwashIdx = cursor.getIntOrNull(cursor.getColumnIndexOrThrow("backwash_idx"))

                val values = ContentValues().apply {
                    put("ord_idx", ordIdx)

                    if (waterpumpIdx != null) { // Atualiza waterpump_ord_idx se waterpump_idx não for nulo
                        val waterpumpCursor = db.rawQuery("SELECT ord_idx FROM $TABLE_DEVICES WHERE idx = ?", arrayOf(waterpumpIdx.toString()))
                        if (waterpumpCursor.moveToFirst()) {
                            put("waterpump_ord_idx", waterpumpCursor.getInt(waterpumpCursor.getColumnIndexOrThrow("ord_idx")))
                        }
                        waterpumpCursor.close()
                    }

                    if (fertiIdx != null) {  // Atualiza ferti_ord_idx se ferti_idx não for nulo
                        val fertiCursor = db.rawQuery("SELECT ord_idx FROM $TABLE_DEVICES WHERE idx = ?", arrayOf(fertiIdx.toString()))
                        if (fertiCursor.moveToFirst()) {
                            put("ferti_ord_idx", fertiCursor.getInt(fertiCursor.getColumnIndexOrThrow("ord_idx")))
                        }
                        fertiCursor.close()
                    }

                    if (backwashIdx != null) {  // Atualiza bachwash_ord_idx se bachwash_idx não for nulo
                        val backwashCursor = db.rawQuery("SELECT ord_idx FROM $TABLE_DEVICES WHERE idx = ?", arrayOf(backwashIdx.toString()))
                        if (backwashCursor.moveToFirst()) {
                            put("backwash_ord_idx", backwashCursor.getInt(backwashCursor.getColumnIndexOrThrow("ord_idx")))
                        }
                        backwashCursor.close()
                    }
                }

                db.update(TABLE_SCHEDULINGS, values, "idx = ?", arrayOf(idx.toString()))
                ordIdx++
            }
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    fun schedulingsReorganizeOrdIdxByCodec(codecIdx: Int) {
        val db = this.writableDatabase
        var cursor: Cursor? = null
        try {
            cursor = db.rawQuery(
                """
            SELECT S.idx, S.waterpump_idx, S.ferti_idx, S.backwash_idx 
            FROM $TABLE_SCHEDULINGS S
            INNER JOIN $TABLE_GROUPS G ON S.group_idx = G.idx
            WHERE G.codec_idx = ?
            ORDER BY S.idx ASC
            """,
                arrayOf(codecIdx.toString())
            )

            var ordIdx = 0
            while (cursor.moveToNext()) {
                val idx = cursor.getInt(cursor.getColumnIndexOrThrow("idx"))
                val waterpumpIdx = cursor.getIntOrNull(cursor.getColumnIndexOrThrow("waterpump_idx"))
                val fertiIdx = cursor.getIntOrNull(cursor.getColumnIndexOrThrow("ferti_idx"))
                val backwashIdx = cursor.getIntOrNull(cursor.getColumnIndexOrThrow("backwash_idx"))

                val values = ContentValues().apply {
                    put("ord_idx", ordIdx)

                    waterpumpIdx?.let {
                        val waterpumpCursor = db.rawQuery(
                            """
                        SELECT D.ord_idx 
                        FROM $TABLE_DEVICES D
                        INNER JOIN $TABLE_MESH_DEVICES M ON D.mesh_idx = M.idx
                        WHERE D.idx = ? AND M.codec_idx = ?
                        """,
                            arrayOf(it.toString(), codecIdx.toString())
                        )
                        if (waterpumpCursor.moveToFirst()) {
                            put("waterpump_ord_idx", waterpumpCursor.getInt(waterpumpCursor.getColumnIndexOrThrow("ord_idx")))
                        }
                        waterpumpCursor.close()
                    }

                    fertiIdx?.let {
                        val fertiCursor = db.rawQuery(
                            """
                        SELECT D.ord_idx 
                        FROM $TABLE_DEVICES D
                        INNER JOIN $TABLE_MESH_DEVICES M ON D.mesh_idx = M.idx
                        WHERE D.idx = ? AND M.codec_idx = ?
                        """,
                            arrayOf(it.toString(), codecIdx.toString())
                        )
                        if (fertiCursor.moveToFirst()) {
                            put("ferti_ord_idx", fertiCursor.getInt(fertiCursor.getColumnIndexOrThrow("ord_idx")))
                        }
                        fertiCursor.close()
                    }

                    backwashIdx?.let {
                        val backwashCursor = db.rawQuery(
                            """
                        SELECT D.ord_idx 
                        FROM $TABLE_DEVICES D
                        INNER JOIN $TABLE_MESH_DEVICES M ON D.mesh_idx = M.idx
                        WHERE D.idx = ? AND M.codec_idx = ?
                        """,
                            arrayOf(it.toString(), codecIdx.toString())
                        )
                        if (backwashCursor.moveToFirst()) {
                            put("backwash_ord_idx", backwashCursor.getInt(backwashCursor.getColumnIndexOrThrow("ord_idx")))
                        }
                        backwashCursor.close()
                    }
                }

                db.update(TABLE_SCHEDULINGS, values, "idx = ?", arrayOf(idx.toString()))
                ordIdx++
            }
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    fun devicesSchedulingsReorganizeOrdIdx() {
        val db = this.writableDatabase
        var cursor: Cursor? = null
        try {
            cursor = db.rawQuery("SELECT idx FROM $TABLE_DEVICE_SCHEDULINGS ORDER BY idx ASC", null)

            var ordIdx = 0
            while (cursor.moveToNext()) {
                val idx = cursor.getInt(cursor.getColumnIndexOrThrow("idx"))

                val values = ContentValues().apply {
                    put("ord_idx", ordIdx)
                }
                db.update(TABLE_DEVICE_SCHEDULINGS, values, "idx = ?", arrayOf(idx.toString()))

                ordIdx++
            }
        } catch (e: SQLException) {
            e.printStackTrace()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    fun devicesSchedulingsReorganizeOrdIdxByCodec(codecIdx: Int) {
        val db = this.writableDatabase
        var cursor: Cursor? = null
        try {
            cursor = db.rawQuery(
                """
            SELECT DS.idx 
            FROM $TABLE_DEVICE_SCHEDULINGS DS
            INNER JOIN $TABLE_SCHEDULINGS S ON DS.scheduling_idx = S.idx
            INNER JOIN $TABLE_GROUPS G ON S.group_idx = G.idx
            WHERE G.codec_idx = ?
            ORDER BY DS.idx ASC
            """,
                arrayOf(codecIdx.toString())
            )

            var ordIdx = 0
            while (cursor.moveToNext()) {
                val idx = cursor.getInt(cursor.getColumnIndexOrThrow("idx"))

                val values = ContentValues().apply {
                    put("ord_idx", ordIdx)
                }
                db.update(TABLE_DEVICE_SCHEDULINGS, values, "idx = ?", arrayOf(idx.toString()))

                ordIdx++
            }
        } catch (e: SQLException) {
            e.printStackTrace()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    fun existsCodecIdentity(identity: String): Boolean {
        return try {
            val db = this.readableDatabase
            val cursor: Cursor = db.rawQuery(
                "SELECT 1 FROM $TABLE_CODECS WHERE identity = ? LIMIT 1",
                arrayOf(identity)
            )
            val exists = cursor.moveToFirst()
            cursor.close()
            exists
        } catch (e: SQLException) {
            e.printStackTrace()
            false
        }
    }

    fun getFertiDeviceIdxByGroup(groupIdx: Int): Int? {
        val type = MeshDeviceFragment.DevType.Ferti.value
        return try {
            val db = this.readableDatabase
            val cursor: Cursor = db.rawQuery(
                """
                SELECT d.idx
                FROM $TABLE_DEVICES d
                JOIN $TABLE_MESH_DEVICES m ON d.mesh_idx = m.idx
                WHERE m.group_idx = ? AND d.type = ?
                LIMIT 1
                """.trimIndent(),
                arrayOf(groupIdx.toString(), type.toString())
            )
            val idx: Int? = if (cursor.moveToFirst()) cursor.getInt(0) else null
            cursor.close()
            idx
        } catch (e: SQLException) {
            e.printStackTrace()
            null
        }
    }

    fun getDeviceIdxByMeshWithBackwash(groupIdx: Int): Int? {
        val type = MeshDeviceFragment.DevType.Backwash.value
        return try {
            val db = this.readableDatabase
            val cursor: Cursor = db.rawQuery(
                """
            SELECT d.idx
            FROM $TABLE_DEVICES d
            JOIN $TABLE_MESH_DEVICES m ON d.mesh_idx = m.idx
            WHERE m.group_idx = ? AND d.type = ?
            LIMIT 1
            """.trimIndent(),
                arrayOf(groupIdx.toString(), type.toString())
            )

            val idx: Int? = if (cursor.moveToFirst()) {
                cursor.getInt(cursor.getColumnIndexOrThrow("idx"))
            } else {
                null
            }

            cursor.close()
            idx
        } catch (e: SQLException) {
            e.printStackTrace()
            null
        }
    }

    fun getLevelPumpDevices(codecIdx: Int): MutableList<MutableMap<String, Any>> {
        val db = this.readableDatabase
        val deviceList = mutableListOf<MutableMap<String, Any>>()
        var cursor: Cursor? = null
        val type = MeshDeviceFragment.MainType.Level.value
        return try {
            val query = """
            SELECT
                d.idx                AS device_id,
                d.mesh_idx           AS device_mesh_idx,
                d.identity           AS device_identity,
                d.type               AS device_type,
                d.ord_idx            AS device_ord_idx,
                dp.ord_idx           AS level_pump_ord_idx,
                md.idx               AS mesh_id,
                md.identity          AS mesh_identity,
                md.level_pump_idx    AS mesh_level_pump_idx,
                md.level_pump_working_time AS level_pump_working_time,
                md.name              AS mesh_name
            FROM Mesh_Devices md
            JOIN Devices d 
                ON d.mesh_idx = md.idx
            LEFT JOIN Devices dp
                ON dp.idx = md.level_pump_idx
            WHERE md.type = ? AND md.level_pump_enable = 1 AND md.codec_idx = ?
        """.trimIndent()

            cursor = db.rawQuery(query, arrayOf(type.toString(), codecIdx.toString()))

            if (cursor.moveToFirst()) {
                do {
                    val item = mutableMapOf<String, Any>()

                    item["li"] = cursor.getInt(cursor.getColumnIndexOrThrow("device_ord_idx"))
                    item["pi"] = cursor.getInt(cursor.getColumnIndexOrThrow("level_pump_ord_idx"))
                    item["wt"] = cursor.getInt(cursor.getColumnIndexOrThrow("level_pump_working_time"))

                    deviceList.add(item)
                } while (cursor.moveToNext())
            }

            deviceList
        } catch (e: SQLException) {
            e.printStackTrace()
            mutableListOf()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    fun getSchedulingsCount(): Int {
        return try {
            val db = this.readableDatabase
            val cursor = db.rawQuery("SELECT COUNT(*) FROM $TABLE_SCHEDULINGS", null)
            var count = 0
            if (cursor.moveToFirst()) {
                count = cursor.getInt(0)
            }
            cursor.close()
            count
        } catch (e: SQLException) {
            e.printStackTrace()
            0
        }
    }

    fun getGroupsCount(): Int {
        return try {
            val db = this.readableDatabase
            val cursor = db.rawQuery("""
            SELECT COUNT(*)
            FROM $TABLE_GROUPS g
            JOIN $TABLE_CODECS c ON g.codec_idx = c.idx
            WHERE c.enabled = 1
        """.trimIndent(), null)

            var count = 0
            if (cursor.moveToFirst()) {
                count = cursor.getInt(0)
            }
            cursor.close()
            count
        } catch (e: SQLException) {
            e.printStackTrace()
            0
        }
    }

    fun getSectorsCount(): Int {
        return try {
            val db = this.readableDatabase
            val cursor = db.rawQuery("""
            SELECT COUNT(*)
            FROM $TABLE_DEVICES d
            JOIN $TABLE_MESH_DEVICES m ON d.mesh_idx = m.idx
            JOIN $TABLE_CODECS c ON m.codec_idx = c.idx
            WHERE d.type = 0 AND c.enabled = 1
        """.trimIndent(), null)

            var count = 0
            if (cursor.moveToFirst()) {
                count = cursor.getInt(0)
            }
            cursor.close()
            count
        } catch (e: SQLException) {
            e.printStackTrace()
            0
        }
    }

    fun getDevicesCount(): Int {
        return try {
            val db = this.readableDatabase
            val cursor = db.rawQuery("""
            SELECT COUNT(*)
            FROM $TABLE_DEVICES d
            JOIN $TABLE_MESH_DEVICES m ON d.mesh_idx = m.idx
            JOIN $TABLE_CODECS c ON m.codec_idx = c.idx
            WHERE c.enabled = 1
        """.trimIndent(), null)

            var count = 0
            if (cursor.moveToFirst()) {
                count = cursor.getInt(0)
            }
            cursor.close()
            count
        } catch (e: SQLException) {
            e.printStackTrace()
            0
        }
    }

    fun getCodecIdxByScheduling(schedulingIdx: Int): Int? {
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            val query = """
            SELECT c.idx AS codec_idx
            FROM $TABLE_SCHEDULINGS s
            JOIN $TABLE_GROUPS g ON s.group_idx = g.idx
            JOIN $TABLE_CODECS c ON g.codec_idx = c.idx
            WHERE s.idx = ?
        """.trimIndent()

            cursor = db.rawQuery(query, arrayOf(schedulingIdx.toString()))
            if (cursor.moveToFirst()) {
                cursor.getInt(cursor.getColumnIndexOrThrow("codec_idx"))
            } else {
                null
            }
        } catch (e: SQLException) {
            e.printStackTrace()
            null
        } finally {
            cursor?.close()
            db.close()
        }
    }

    fun getCodecIdxByGroup(groupIdx: Int): Int? {
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            val query = """
            SELECT c.idx AS codec_idx
            FROM $TABLE_GROUPS g
            JOIN $TABLE_CODECS c ON g.codec_idx = c.idx
            WHERE g.idx = ?
        """.trimIndent()

            cursor = db.rawQuery(query, arrayOf(groupIdx.toString()))
            if (cursor.moveToFirst()) {
                cursor.getInt(cursor.getColumnIndexOrThrow("codec_idx"))
            } else {
                null
            }
        } catch (e: SQLException) {
            e.printStackTrace()
            null
        } finally {
            cursor?.close()
            db.close()
        }
    }

    fun getCodecIdxByDevice(deviceIdx: Int): Int? {
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            val query = """
            SELECT c.idx AS codec_idx
            FROM $TABLE_DEVICES d
            JOIN $TABLE_MESH_DEVICES m ON d.mesh_idx = m.idx
            JOIN $TABLE_CODECS c ON m.codec_idx = c.idx
            WHERE d.idx = ?
        """.trimIndent()

            cursor = db.rawQuery(query, arrayOf(deviceIdx.toString()))

            if (cursor.moveToFirst()) {
                cursor.getInt(cursor.getColumnIndexOrThrow("codec_idx"))
            } else {
                null
            }
        } catch (e: SQLException) {
            e.printStackTrace()
            null
        } finally {
            cursor?.close()
            db.close()
        }
    }


    fun getLevelPumpEnableByDevice(deviceIdx: Int): Boolean? {
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            val query = """
            SELECT level_pump_enable 
            FROM $TABLE_MESH_DEVICES 
            WHERE level_pump_idx = ?
        """.trimIndent()

            cursor = db.rawQuery(query, arrayOf(deviceIdx.toString()))

            if (cursor.moveToFirst()) {
                cursor.getInt(cursor.getColumnIndexOrThrow("level_pump_enable")) == 1
            } else {
                null
            }
        } catch (e: SQLException) {
            e.printStackTrace()
            null
        } finally {
            cursor?.close()
            db.close()
        }
    }

    fun updateLevelPumpEnableByDevice(deviceIdx: Int, enabled: Boolean): Int {
        val db = this.writableDatabase
        return try {
            val values = ContentValues().apply {
                put("level_pump_enable", if (enabled) 1 else 0)
            }

            db.update(
                TABLE_MESH_DEVICES,
                values,
                "level_pump_idx = ?",
                arrayOf(deviceIdx.toString())
            )
        } catch (e: SQLException) {
            e.printStackTrace()
            -1
        } finally {
            db.close()
        }
    }

    fun isTimeInsideExistingInterval(schedulingIdxToIgnore: Int, groupIdx: Int, startTime: Int, endTime: Int, daysOfWeek: Int): Boolean {
        val db = this.readableDatabase

        val query = """
        SELECT COUNT(*) FROM $TABLE_SCHEDULINGS
        WHERE group_idx = ?
          AND (
              (? >= start_time AND ? <= end_time)
              OR
              (? >= start_time AND ? <= end_time)
          )
          AND (days_of_week & ?) != 0 AND idx != ?
    """.trimIndent()

        val cursor = db.rawQuery(query, arrayOf(
            groupIdx.toString(),
            startTime.toString(), startTime.toString(),
            endTime.toString(), endTime.toString(),
            daysOfWeek.toString(), schedulingIdxToIgnore.toString()
        ))

        cursor.use {
            if (it.moveToFirst()) {
                val count = it.getInt(0)
                return count > 0
            }
        }

        return false
    }

    fun Cursor.getIntOrNull(columnIndex: Int): Int? {
        return if (isNull(columnIndex)) null else getInt(columnIndex)
    }
}