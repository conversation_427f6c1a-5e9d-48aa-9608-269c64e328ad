package br.com.byagro.irriganet

import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView


class DeviceAdapter(
    private val deviceList: MutableList<DeviceItem>,
    private val onItemClick: (DeviceItem) -> Unit
) :
    RecyclerView.Adapter<DeviceAdapter.DeviceViewHolder>() {

    class DeviceViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val textViewTitle: TextView = itemView.findViewById(R.id.item_title)
        val textViewId: TextView = itemView.findViewById(R.id.item_id)
        val imageViewSync: ImageView = itemView.findViewById(R.id.item_sync)
        val imageViewOn: ImageView = itemView.findViewById(R.id.item_on)

        fun bind(item: DeviceItem, onItemClick: (DeviceItem) -> Unit) {
            itemView.setOnClickListener {
                onItemClick(item)
            }

        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DeviceViewHolder {
        val itemView = LayoutInflater.from(parent.context).inflate(R.layout.item_model_devices, parent, false)
        return DeviceViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: DeviceViewHolder, position: Int) {
        val currentDevice = deviceList[position]
        holder.textViewTitle.text = currentDevice.name
        holder.textViewId.text = "ID: "+currentDevice.meshIdentity+"-"+(currentDevice.deviceIdentity.toInt()+1).toString()
        holder.bind(currentDevice, onItemClick)

        if(currentDevice.sync != null){
            if (currentDevice.sync == true) {
                holder.imageViewSync.setColorFilter(Color.parseColor("#4CAF50"))
            } else {
                holder.imageViewSync.setColorFilter(Color.parseColor("#FFEB3B"))
            }
        } else {
            holder.imageViewSync.setColorFilter(Color.parseColor("#BDBDBD"))
        }

        if(currentDevice.on != null){
            if (currentDevice.on == true) {
                holder.imageViewOn.setColorFilter(Color.parseColor("#4CAF50"))
            } else {
                holder.imageViewOn.setColorFilter(Color.parseColor("#000000"))
            }
        } else {
            holder.imageViewOn.setColorFilter(Color.parseColor("#BDBDBD"))
        }
    }

    override fun getItemCount() = deviceList.size
}