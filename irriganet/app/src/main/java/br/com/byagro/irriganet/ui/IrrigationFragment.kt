package br.com.byagro.irriganet.ui

import android.app.AlertDialog
import android.content.Context
import android.content.SharedPreferences
import android.graphics.Color
import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import br.com.byagro.irriganet.DBHelper
import br.com.byagro.irriganet.GroupAdapter
import br.com.byagro.irriganet.GroupItem
import br.com.byagro.irriganet.IrrigationGroupAdapter
import br.com.byagro.irriganet.MainActivity
import br.com.byagro.irriganet.R
import br.com.byagro.irriganet.SharedData
import br.com.byagro.irriganet.databinding.FragmentIrrigationBinding
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

// TODO: Rename parameter arguments, choose names that match
// the fragment initialization parameters, e.g. ARG_ITEM_NUMBER
private const val ARG_PARAM1 = "param1"
private const val ARG_PARAM2 = "param2"

/**
 * A simple [Fragment] subclass.
 * Use the [IrrigationFragment.newInstance] factory method to
 * create an instance of this fragment.
 */
class IrrigationFragment : Fragment() {
    private val PREFS_NAME = "byagro_prefs"
    private lateinit var binding: FragmentIrrigationBinding
    private lateinit var sharedPref: SharedPreferences
    private lateinit var dbHelper: DBHelper
    private var job: Job? = null

    private var codecIdx: Int = 0
    private var codecIdentity: String? = null
    private var codecName: String? = null
    private var pauseScheduling: Boolean = false

    private lateinit var adapter: IrrigationGroupAdapter
    private var groupList: MutableList<GroupItem> = mutableListOf()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {

        }
        dbHelper = DBHelper(requireContext())
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        binding = FragmentIrrigationBinding.inflate(inflater, container, false)

        try {
            sharedPref = requireContext().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

            binding.fragIrrigationRecyclerViewGroups.layoutManager = LinearLayoutManager(context)

            adapter = IrrigationGroupAdapter(groupList, { selectedItem -> onItemClick(selectedItem) })
            binding.fragIrrigationRecyclerViewGroups.adapter = adapter

            groupList.clear()
            groupList.addAll(dbHelper.getAllGroups(null))
            adapter.notifyDataSetChanged()

            pauseScheduling = sharedPref.getBoolean("pauseScheduling", false)

            val codecList = dbHelper.getAllCodecsOnMap()

            val activity = activity as MainActivity

            job = CoroutineScope(Dispatchers.IO).launch {
                while (true) {
                    try {
                        try {
                            val latestCodecsUpdates = activity.getLatestCodecsUpdates()
                            var countCodecsPaused = 0
                            for (codec in codecList) {
                                val identity = codec["identity"] as String
                                if (latestCodecsUpdates[identity]?.pauseScheduling == true) {
                                    countCodecsPaused += 1 // Conta os codecs pausados
                                }
                            }
                            if (countCodecsPaused == codecList.size) { // Se todos os codecs estiverem pausados
                                pauseScheduling = true
                            }else{
                                pauseScheduling = false
                            }
                        } finally {
                        }

                        withContext(Dispatchers.Main) {
                            if(groupList.size > 0) {
                                binding.fragIrrigationButtonPauseScheduling.visibility = View.VISIBLE
                                if (pauseScheduling) {
                                    binding.fragIrrigationButtonPauseScheduling.setText("Retomar Agenda")
                                } else {
                                    binding.fragIrrigationButtonPauseScheduling.setText("Pausar Agenda")
                                }
                            } else {
                                binding.fragIrrigationButtonPauseScheduling.visibility = View.GONE
                            }
                        }

                    } catch (e: Exception){
                        e.printStackTrace()
                    }

                    delay(1000)
                }
            }

            binding.fragIrrigationButtonPauseScheduling.setOnClickListener {
                if (pauseScheduling) {
                    //binding.fragIrrigationButtonPauseScheduling.setText("Pausar Agenda")
                    with(sharedPref.edit()) {
                        putInt("schedulePauseDuration", 0)
                        putBoolean("pauseScheduling", false)
                        putBoolean("pauseUpdated", true)
                        putBoolean("dataUpdateEvent", true)
                        apply()
                    }
                } else {
                    showPauseSchedulingDialog()
                }
            }
        } catch (e: Exception){
            e.printStackTrace()
        }

        return binding.root
    }

    private fun showPauseSchedulingDialog() {
        val dialogView = LayoutInflater.from(context).inflate(R.layout.dialog_pause_input, null)
        val editText: EditText = dialogView.findViewById(R.id.editText)

        editText.setText("24")

        // Build the AlertDialog
        val dialogBuilder = AlertDialog.Builder(requireContext())
            .setView(dialogView)
            .setTitle("Duração da pausa (horas)")
            .setPositiveButton("OK") { dialog, _ ->
                val inputText = editText.text.toString()
                if (inputText.isNotEmpty()) {
                    //binding.fragIrrigationButtonPauseScheduling.setText("Retomar Agenda")
                    val schedulePauseDuration = inputText.toInt()*60 // Converter de horas para minutos
                    with(sharedPref.edit()) {
                        putInt("schedulePauseDuration", schedulePauseDuration)
                        putBoolean("pauseScheduling", true)
                        putBoolean("pauseUpdated", true)
                        putBoolean("dataUpdateEvent", true)
                        apply()
                    }
                } else {

                }
                dialog.dismiss()
            }
            .setNegativeButton("Cancelar") { dialog, _ ->
                dialog.cancel()
            }

        // Create and show the dialog
        val alertDialog = dialogBuilder.create()
        alertDialog.show()
    }

    private fun onItemClick(Item: GroupItem) {
        val bundle = Bundle().apply {
            putInt("codecIdx", codecIdx)
            putInt("groupIdx", Item.idx)
            putString("groupName", Item.name)
        }
        findNavController().navigate(R.id.action_nav_irrigation_to_nav_group_scheduling, bundle)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        job?.cancel()
    }
}