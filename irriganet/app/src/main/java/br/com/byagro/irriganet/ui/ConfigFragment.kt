package br.com.byagro.irriganet.ui

import android.app.AlertDialog
import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import androidx.navigation.fragment.findNavController
import br.com.byagro.irriganet.DBHelper
import br.com.byagro.irriganet.GroupItem
import br.com.byagro.irriganet.MainActivity
import br.com.byagro.irriganet.R
import br.com.byagro.irriganet.SharedData
import br.com.byagro.irriganet.databinding.FragmentConfigBinding
import br.com.byagro.irriganet.databinding.FragmentSectorSchedulingBinding
import com.google.gson.Gson

// TODO: Rename parameter arguments, choose names that match
// the fragment initialization parameters, e.g. ARG_ITEM_NUMBER
private const val ARG_PARAM1 = "param1"
private const val ARG_PARAM2 = "param2"

/**
 * A simple [Fragment] subclass.
 * Use the [ConfigFragment.newInstance] factory method to
 * create an instance of this fragment.
 */
class ConfigFragment : Fragment() {
    // TODO: Rename and change types of parameters
    private val PREFS_NAME = "byagro_prefs"
    private lateinit var binding: FragmentConfigBinding
    private lateinit var sharedPref: SharedPreferences
    private lateinit var dbHelper: DBHelper
    private var backwashCycle: String? = null
    private var backwashDuration: String? = null
    private var fertiWash: String? = null
    private var rainGaugeEnabled: Boolean = false
    private var rainGaugeResolution: String? = null
    private var rainfallLimit: String? = null
    private var rainfallPauseDuration: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {

        }
        dbHelper = DBHelper(requireContext())
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        binding = FragmentConfigBinding.inflate(inflater, container, false)

        try {
            val activity = activity as? MainActivity
            //mqttManager = activity?.getMqttManager()

            sharedPref = requireContext().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

            backwashCycle = sharedPref.getString("backwashCycle", "120")
            backwashDuration = sharedPref.getString("backwashDuration", "2")
            fertiWash = sharedPref.getString("fertiWash", "1")
            rainGaugeEnabled = sharedPref.getBoolean("rainGaugeEnabled", false)
            rainGaugeResolution = sharedPref.getString("rainGaugeResolution", "0.2")
            rainfallLimit = sharedPref.getString("rainfallLimit", "2")
            rainfallPauseDuration = sharedPref.getString("rainfallPauseDuration", "24")

            if (rainGaugeEnabled) {
                binding.fragConfigLinearLayoutRaingauge.visibility = View.VISIBLE
            } else {
                binding.fragConfigLinearLayoutRaingauge.visibility = View.GONE
            }

            binding.fragConfigTextInputBackwashCycle.setText(backwashCycle)
            binding.fragConfigTextInputBackwashDuration.setText(backwashDuration)
            binding.fragConfigTextInputFertiWash.setText(fertiWash)
            binding.fragConfigSwitchRaingaugeEnable.isChecked = rainGaugeEnabled
            binding.fragConfigTextInputRaingaugeResolution.setText(rainGaugeResolution)
            binding.fragConfigTextInputRainfallLimit.setText(rainfallLimit)
            binding.fragConfigTextInputRainfallPauseDuration.setText(rainfallPauseDuration)

            binding.fragConfigTextInputBackwashCycle.addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(
                    s: CharSequence?,
                    start: Int,
                    count: Int,
                    after: Int
                ) {
                }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
                override fun afterTextChanged(s: Editable?) {
                    backwashCycle = s.toString()
                }
            })

            binding.fragConfigTextInputBackwashDuration.addTextChangedListener(object :
                TextWatcher {
                override fun beforeTextChanged(
                    s: CharSequence?,
                    start: Int,
                    count: Int,
                    after: Int
                ) {
                }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
                override fun afterTextChanged(s: Editable?) {
                    backwashDuration = s.toString()
                }
            })

            binding.fragConfigTextInputFertiWash.addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(
                    s: CharSequence?,
                    start: Int,
                    count: Int,
                    after: Int
                ) {
                }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
                override fun afterTextChanged(s: Editable?) {
                    fertiWash = s.toString()
                }
            })

            binding.fragConfigTextInputRaingaugeResolution.addTextChangedListener(object :
                TextWatcher {
                override fun beforeTextChanged(
                    s: CharSequence?,
                    start: Int,
                    count: Int,
                    after: Int
                ) {
                }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
                override fun afterTextChanged(s: Editable?) {
                    rainGaugeResolution = s.toString()
                }
            })

            binding.fragConfigTextInputRainfallLimit.addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(
                    s: CharSequence?,
                    start: Int,
                    count: Int,
                    after: Int
                ) {
                }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
                override fun afterTextChanged(s: Editable?) {
                    rainfallLimit = s.toString()
                }
            })

            binding.fragConfigTextInputRainfallPauseDuration.addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(
                    s: CharSequence?,
                    start: Int,
                    count: Int,
                    after: Int
                ) {
                }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
                override fun afterTextChanged(s: Editable?) {
                    rainfallPauseDuration = s.toString()
                }
            })

            binding.fragConfigSwitchRaingaugeEnable.setOnCheckedChangeListener { _, isChecked ->
                rainGaugeEnabled = isChecked
                if (rainGaugeEnabled) {
                    binding.fragConfigLinearLayoutRaingauge.visibility = View.VISIBLE
                } else {
                    binding.fragConfigLinearLayoutRaingauge.visibility = View.GONE
                }
            }

            binding.fragConfigButtonCodecs.setOnClickListener {
                val bundle = Bundle().apply {
                }
                findNavController().navigate(R.id.action_nav_config_to_nav_codecs, bundle)
            }

            binding.fragConfigButtonDevices.setOnClickListener {
                val bundle = Bundle().apply {
                }
                findNavController().navigate(R.id.action_nav_config_to_nav_devices_manager, bundle)
            }

            binding.fragConfigButtonProfile.setOnClickListener {
                val bundle = Bundle().apply {
                }
                findNavController().navigate(R.id.action_nav_config_to_nav_profile, bundle)
            }
        } catch (e: Exception){
            e.printStackTrace()
        }

        return binding.root
    }

    override fun onPause() {
        super.onPause()
        try {
            if (backwashCycle != sharedPref.getString(
                    "backwashCycle",
                    ""
                ) || backwashDuration != sharedPref.getString("backwashDuration", "") ||
                fertiWash != sharedPref.getString(
                    "fertiWash",
                    ""
                ) || rainGaugeEnabled != sharedPref.getBoolean(
                    "rainGaugeEnabled",
                    false
                ) || rainGaugeResolution != sharedPref.getString("rainGaugeResolution", "") ||
                rainfallLimit != sharedPref.getString(
                    "rainfallLimit",
                    ""
                ) || rainfallPauseDuration != sharedPref.getString(
                    "rainfallPauseDuration",
                    ""
                ) ) {
                val timeStamp = System.currentTimeMillis() / 1000
                val codecList = dbHelper.getAllCodecsOnMap()
                for (codec in codecList) {
                    dbHelper.updateCodecFields(
                        codec["idx"] as Int ?: 0, mapOf(
                            "last_config_update" to timeStamp
                        )
                    )
                }
                with(sharedPref.edit()) {
                    putString("backwashCycle", backwashCycle)
                    putString("backwashDuration", backwashDuration)
                    putString("fertiWash", fertiWash)
                    putBoolean("rainGaugeEnabled", rainGaugeEnabled)
                    putString("rainGaugeResolution", rainGaugeResolution)
                    putString("rainfallLimit", rainfallLimit)
                    putString("rainfallPauseDuration", rainfallPauseDuration)
                    putBoolean("dataUpdated", true)
                    putBoolean("dataUpdateEvent", true)
                    apply()
                }
            }
        } catch (e: Exception){
            e.printStackTrace()
        }
    }
}