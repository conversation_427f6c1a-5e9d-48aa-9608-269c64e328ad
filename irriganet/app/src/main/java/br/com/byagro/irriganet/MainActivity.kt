package br.com.byagro.irriganet

import HiveMqttManager
import android.app.AlertDialog
import android.content.Context
import android.content.SharedPreferences
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import android.os.Bundle
import android.util.Base64
import android.util.Log
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuItem
import android.widget.EditText
import android.widget.TextView
import android.widget.Toast
import com.google.android.material.snackbar.Snackbar
import com.google.android.material.navigation.NavigationView
import androidx.navigation.findNavController
import androidx.navigation.ui.AppBarConfiguration
import androidx.navigation.ui.navigateUp
import androidx.navigation.ui.setupActionBarWithNavController
import androidx.navigation.ui.setupWithNavController
import androidx.drawerlayout.widget.DrawerLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavOptions
import br.com.byagro.irriganet.databinding.ActivityMainBinding
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.json.JSONObject
import br.com.byagro.irriganet.SharedData
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.ToNumberPolicy
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.runBlocking
import java.lang.reflect.Type
import br.com.byagro.irriganet.Utils
import codec.`in`.IncomingPacketOuterClass
import com.upokecenter.cbor.CBORObject
import codec.`in`.config.Config
import codec.`in`.devices.Devices
import codec.`in`.scheduling.SchedulingOuterClass
import codec.`in`.device_scheduling.DeviceSchedulingOuterClass
import codec.`in`.scheduling.SchedulingOuterClass.Scheduling
import codec.`in`.automation.Automation
import codec.`in`.request_info.RequestInfo
import codec.`in`.firmware_update.FirmwareUpdate
import codec.`in`.pause.Pause
import codec.out.OutgoingPacketOuterClass
import com.android.volley.BuildConfig
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.ObjectInputStream
import java.io.ObjectOutputStream
import java.util.UUID
import java.util.concurrent.ConcurrentHashMap


class MainActivity : AppCompatActivity() {

    data class LatestCodecUpdates(
        var codecFirmware: Int?,
        var pauseScheduling: Boolean?,
        var schedulingRunning: Boolean?,
        var devicesId: Long?,
        var schedulingId: Long?,
        var devSchedulingId: Long?,
        var automationId: Long?,
        var configId: Long?,
        var lastInfo: Long?,
        var syncBitmask: Long?,
        var onBitmask: Long?,
        var inputBitmask: Long?,
        var failedBitmask: Long?,
        var rainStatus: Boolean?,
        var rainfall: Int?,
        var updated: Boolean?
    )

    private val PREFS_NAME = "byagro_prefs"
    private lateinit var appBarConfiguration: AppBarConfiguration
    private lateinit var binding: ActivityMainBinding
    private lateinit var dbHelper: DBHelper
    private lateinit var mqttManager: HiveMqttManager
    private var job: Job? = null
    private lateinit var sharedPref: SharedPreferences
    private lateinit var mqttViewModel: MqttViewModel
    private lateinit var latestCodecUpdates: LatestCodecUpdates
    private val codecUpdateOnMap: ConcurrentHashMap<String, LatestCodecUpdates> = ConcurrentHashMap()
    private lateinit var codecList: List<Map<String, Any>>
    private val messageList: ConcurrentHashMap<Pair<String, Int>, ByteArray> = ConcurrentHashMap()
    private var schedulingRunningInfoTime = System.currentTimeMillis()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setSupportActionBar(binding.appBarMain.toolbar)

        mqttViewModel = ViewModelProvider(this).get(MqttViewModel::class.java)

        dbHelper = DBHelper(this)
        sharedPref = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

        val drawerLayout: DrawerLayout = binding.drawerLayout
        val navView: NavigationView = binding.navView
        val navController = findNavController(R.id.nav_host_fragment_content_main)
        // Passing each menu ID as a set of Ids because each
        // menu should be considered as top level destinations.
        appBarConfiguration = AppBarConfiguration(
            setOf(
                R.id.nav_report, R.id.nav_irrigation, R.id.nav_individual_pumps
            ), drawerLayout
        )
        setupActionBarWithNavController(navController, appBarConfiguration)
        navView.setupWithNavController(navController)

        if (dbHelper.getCodecsCount() == 0) {
            navController.navigate(R.id.nav_codec_reg)
        }

        codecList = dbHelper.getAllCodecsOnMap()
        SharedData.codecWifiIsConnected.set(false)
        loadMessageList()

        val topics = mutableListOf<String>()
        for (codec in codecList) {
            val identity = codec["identity"] as String
            topics.add("/codec/$identity/report")
        }

        mqttManager = HiveMqttManager(
            serverHost = "mosquitto-codec.saas.byagro.dev.br",
            clientId   = "IrrigaNet-"+UUID.randomUUID(),
            user       = "codec",
            pass       = "Y29kZWM=",
            topics     = topics
        ) { topic, data ->
            val hex = data.joinToString(" ") { "%02X".format(it) }
            Log.i("MQTT", "MQTT: [$topic] $hex")

            val identity = topic.split("/").getOrNull(2)
            handleResponse(identity, data)
        }

        lifecycleScope.launch {
            mqttManager.connectAndWait()
        }

        job = CoroutineScope(Dispatchers.IO).launch {
            var basicInfoTime = System.currentTimeMillis()-5000
            var isActive = true
            var countCodecUpdated = 0
            while (isActive) {
                try {
                    if (System.currentTimeMillis() - basicInfoTime > 10000) {
                        basicInfoTime = System.currentTimeMillis()

                        if (isConnectedToWifi(this@MainActivity) && !SharedData.codecWifiIsConnected.get()) { // Se estiver conectado ao WiFi do Codec, e ainda não estiver obtido os dados do Codec
                            WebService.getCodecInfo(this@MainActivity, OutgoingPacketOuterClass.OutgoingPacket.PayloadCase.INFO.number.toByte(), ::handleResponse) // Obter informações do Codec
                        }

                        if (SharedData.codecWifiIsConnected.get()) {
                            WebService.getCodecInfo(this@MainActivity, OutgoingPacketOuterClass.OutgoingPacket.PayloadCase.STATUS.number.toByte(), ::handleResponse)
                        }

                        if (mqttManager.isConnected()) { // Se estiver conectado ao MQTT e não estiver atualizando, enviar requisição de info
                            for (codec in codecList) {
                                val identity = codec["identity"] as String
                                if (codecUpdateOnMap[identity] == null) {
                                    val topic = "/codec/$identity/downlink"
                                    val requestInfo = RequestInfo.RequestInfoPackage.newBuilder()
                                        .setType(0)
                                        .build()

                                    val packet = IncomingPacketOuterClass.IncomingPacket.newBuilder()
                                        .setId(System.currentTimeMillis() / 1000)
                                        .setRequestInfo(requestInfo)
                                        .build()

                                    val payload: ByteArray = packet.toByteArray()
                                    val crc2: Int = Utils.crc16(payload)
                                    val crcBytes = byteArrayOf(((crc2 ushr 8) and 0xFF).toByte(), (crc2 and 0xFF).toByte())
                                    val finalPayload = payload + crcBytes

                                    mqttManager.publish(topic, finalPayload)
                                }
                            }
                        }
                    }

                    if (sharedPref.getBoolean("dataUpdated", false)) { // Verifica se é necessário atualizar os dados
                        if (sharedPref.getBoolean("dataUpdateEvent", false)) {
                            //dbHelper.devicesReorganizeOrdIdx()
                            codecList = dbHelper.getAllCodecsOnMap()
                            for (codec in codecList) {
                                val codecIdx = codec["idx"] as Int
                                dbHelper.devicesReorganizeOrdIdxByCodec(codecIdx)
                                dbHelper.schedulingsReorganizeOrdIdxByCodec(codecIdx)
                                dbHelper.devicesSchedulingsReorganizeOrdIdxByCodec(codecIdx)
                            }
                            with(sharedPref.edit()) {
                                putBoolean("dataUpdateEvent", false)
                                apply()
                            }
                        }
                        countCodecUpdated = 0
                        if (codecUpdateOnMap.isNotEmpty()) {
                            for (codec in codecList) {
                                try {
                                    val identity = codec["identity"] as String
                                    if (codec["last_devices_update"] as Long > codecUpdateOnMap[identity]?.devicesId!! || codec["last_scheduling_update"] as Long > codecUpdateOnMap[identity]?.schedulingId!! ||
                                        codec["last_device_scheduling_update"] as Long > codecUpdateOnMap[identity]?.devSchedulingId!! || codec["last_automation_update"] as Long > codecUpdateOnMap[identity]?.automationId!! ||
                                        codec["last_config_update"] as Long > codecUpdateOnMap[identity]?.configId!!
                                    ) {
                                        updateCodec()
                                        codecUpdateOnMap[identity]?.updated = false
                                    } else {
                                        countCodecUpdated += 1
                                        codecUpdateOnMap[identity]?.updated = true
                                    }
                                } catch (e: Exception) {
                                    e.printStackTrace()
                                }
                            }
                        }
                        if (countCodecUpdated == codecList.size || (SharedData.codecWifiIsConnected.get() && codecUpdateOnMap[SharedData.codecId.get()
                                .toString()]?.updated!!)
                        ) {
                            with(sharedPref.edit()) {
                                putBoolean("dataUpdated", false)
                                putBoolean("dataUpdateEvent", false)
                                apply()
                            }
                        }
                    }

                    if (sharedPref.getBoolean("pauseUpdated", false)) { // Verifica se é necessário pausar os agendamentos
                        for (codec in codecList) {
                            val identity = codec["identity"] as String
                            val topic = "/codec/$identity/downlink"

                            val pause = Pause.PauseSchedulingPackage.newBuilder()
                                .setState(if (sharedPref.getBoolean("pauseScheduling", false)) 1 else 0)
                                .setDuration(sharedPref.getInt("schedulePauseDuration", 0))
                                .build()

                            val packet = IncomingPacketOuterClass.IncomingPacket.newBuilder()
                                .setId(System.currentTimeMillis() / 1000)
                                .setPause(pause)
                                .build()

                            val payload: ByteArray = packet.toByteArray()
                            val crc2: Int = Utils.crc16(payload)
                            val crcBytes = byteArrayOf(((crc2 ushr 8) and 0xFF).toByte(), (crc2 and 0xFF).toByte())
                            val finalPayload = payload + crcBytes

                            if (SharedData.codecWifiIsConnected.get()) {
                                WebService.getCodecInfo(this@MainActivity, OutgoingPacketOuterClass.OutgoingPacket.PayloadCase.INFO.number.toByte(), ::handleResponse)
                            } else {
                                mqttManager.publish(topic, finalPayload)
                            }
                        }

                        with(sharedPref.edit()) {
                            putBoolean("pauseUpdated", false)
                            apply()
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }

                delay(2000)
            }
        }

    }

    fun updateCodec() = lifecycleScope.launch {
        for (codec in codecList) {
            val identity = codec["identity"] as String
            val topic = "/codec/$identity/downlink"

            try {
                if(codecUpdateOnMap[identity]?.schedulingRunning == true && (codec["last_devices_update"] as Long > codecUpdateOnMap[identity]?.devicesId!! || codec["last_scheduling_update"] as Long > codecUpdateOnMap[identity]?.schedulingId!! || codec["last_device_scheduling_update"] as Long > codecUpdateOnMap[identity]?.devSchedulingId!!)){
                    if (System.currentTimeMillis() - schedulingRunningInfoTime > 10000) {
                        schedulingRunningInfoTime = System.currentTimeMillis()
                        runOnUiThread {
                            val snackbar = Snackbar.make(findViewById(android.R.id.content), "A configuração será realizada após a conclusão do agendamento em andamento.", Snackbar.LENGTH_LONG)
                            snackbar.view.findViewById<TextView>(com.google.android.material.R.id.snackbar_text).maxLines = 8
                            snackbar.show()
                            //Toast.makeText( this@MainActivity, "A configuração será realizada após a conclusão do agendamento em andamento.", Toast.LENGTH_LONG).show()
                        }
                    }
                } else {
                    if (codec["last_devices_update"] as Long > codecUpdateOnMap[identity]?.devicesId!!) { // Verifica se é necessário atualizar os dipositivos do Codec
                        launch {
                            val time = codec["last_devices_update"] as Long
                            val mapList = dbHelper.getDevicesByCodec(codec["idx"] as Int ?: 0)

                            val meshDevices = mapList.map { item ->
                                Devices.DevicesData.newBuilder()
                                    .setIdx(item["ix"] as Int)
                                    .setMeshId(item["mi"] as Int)
                                    .setDeviceId(item["di"] as Int)
                                    .setDeviceType(item["tp"] as Int)
                                    .setOut1(item["o1"] as Int)
                                    .setOut2(item["o2"] as Int)
                                    .setInput(item["ip"] as Int)
                                    .setMode(item["md"] as Int)
                                    .setSector(item["sc"] as Int)
                                    .setGroupIdx(item["gp"] as Int)
                                    .setEqptVer(item["eq"] as Int)
                                    .build()
                            }
                            val devicePackage = Devices.DevicesPackage.newBuilder()
                                .addAllData(meshDevices)
                                .build()

                            val packet = IncomingPacketOuterClass.IncomingPacket.newBuilder()
                                .setId(time)
                                .setDevices(devicePackage)
                                .build()

                            val payload: ByteArray = packet.toByteArray()
                            val crc2: Int = Utils.crc16(payload)
                            val crcBytes = byteArrayOf(
                                ((crc2 ushr 8) and 0xFF).toByte(),
                                (crc2 and 0xFF).toByte()
                            )
                            val finalPayload = payload + crcBytes

                            println("Payload size: ${payload.size}")
                            println(payload.joinToString(" ") { "%02X".format(it) })

                            if (SharedData.codecWifiIsConnected.get() && SharedData.codecId.get() == identity) {
                                WebService.Request(
                                    identity,
                                    finalPayload,
                                    this@MainActivity,
                                    ::handleResponse
                                )
                                launch {
                                    delay(1000L)
                                    WebService.getCodecInfo(this@MainActivity, OutgoingPacketOuterClass.OutgoingPacket.PayloadCase.INFO.number.toByte(), ::handleResponse)
                                }
                            } else if (mqttManager.isConnected()) {
                                mqttManager.publish(topic, finalPayload)
                            }
                        }
                        return@launch
                    } else {

                    }

                    if (codec["last_scheduling_update"] as Long > codecUpdateOnMap[identity]?.schedulingId!!) { // Verifica se é necessário atualizar os agendamentos do Codec
                        launch {
                            val time = codec["last_scheduling_update"] as Long
                            val shedulingsList = dbHelper.getSchedulingsByCodec(
                                codec["idx"] as Int ?: 0,
                                nicknames = true
                            )

                            val scheduling = shedulingsList.map { item ->
                                Scheduling.newBuilder()
                                    .setIdx(item["ix"] as Int)                     // ord_idx -> idx
                                    .setStartTime(item["st"] as Int)               // start_time
                                    .setDaysOfWeek(item["dw"] as Int)              // days_of_week
                                    .setNumberOfSteps(item["ns"] as Int)           // number_of_steps
                                    .setAllowFerti(item["af"] as Int)              // allow_ferti
                                    .setAllowBackwash(item["ab"] as Int)           // allow_backwash
                                    .setWaterpumpIdx(item["wi"] as Int)            // waterpump_ord_idx
                                    .setWaterpumpWorkingTime(item["wt"] as Int)    // waterpump_working_time
                                    .setFertiIdx(item["fi"] as Int)                // ferti_ord_idx
                                    .setBackwashIdx(item["bi"] as Int)             // backwash_ord_idx
                                    .setGroup(item["gi"] as Int)                   // group_idx
                                    .build()
                            }

                            val schedulingPackage = SchedulingOuterClass.SchedulingPackage.newBuilder()
                                    .setType(SchedulingOuterClass.MsgType.MSG_SCHEDULING_ONLY)
                                    .addAllSchedulingData(scheduling)
                                    .build()

                            val packet = IncomingPacketOuterClass.IncomingPacket.newBuilder()
                                .setId(time)
                                .setScheduling(schedulingPackage)
                                .build()

                            val payload: ByteArray = packet.toByteArray()
                            val crc2: Int = Utils.crc16(payload)
                            val crcBytes = byteArrayOf(
                                ((crc2 ushr 8) and 0xFF).toByte(),
                                (crc2 and 0xFF).toByte()
                            )
                            val finalPayload = payload + crcBytes

                            if (SharedData.codecWifiIsConnected.get() && SharedData.codecId.get() == identity) {
                                WebService.Request(
                                    identity,
                                    finalPayload,
                                    this@MainActivity,
                                    ::handleResponse
                                )
                                launch {
                                    delay(1000L)
                                    WebService.getCodecInfo(this@MainActivity, OutgoingPacketOuterClass.OutgoingPacket.PayloadCase.INFO.number.toByte(), ::handleResponse)
                                }
                            } else if (mqttManager.isConnected()) {
                                mqttManager.publish(topic, finalPayload)
                            }
                        }
                        return@launch
                    }

                    if (codec["last_device_scheduling_update"] as Long > codecUpdateOnMap[identity]?.devSchedulingId!!) { // Verifica se é necessário atualizar os agendamentos dos dispositivos do Codec
                        launch {
                            val time = codec["last_device_scheduling_update"] as Long
                            val deviceShedulingsList =
                                dbHelper.getDevicesSchedulingsByCodec(codec["idx"] as Int ?: 0)

                            val deviceScheduling = deviceShedulingsList.map { item ->
                                DeviceSchedulingOuterClass.DeviceScheduling.newBuilder()
                                    .setIdx(item["ix"] as Int)                            // ord_idx -> idx
                                    .setSchedulingIdx(item["sh"] as Int)                   // scheduling_ord_idx -> scheduling_idx
                                    .setDeviceIdx(item["dx"] as Int)                       // device_ord_idx -> device_idx
                                    .setOrder(item["od"] as Int)                           // n_order -> order
                                    .setSectorWorkingTime(item["st"] as Int)               // sector_working_time
                                    .setFertiWorkingTime(item["ft"] as Int)                // ferti_working_time
                                    .setFertiDelay(item["fd"] as Int)                      // ferti_delay
                                    .build()
                            }

                            val devSchedulingPackage = DeviceSchedulingOuterClass.DeviceSchedulingPackage.newBuilder() // Cria o DeviceSchedulingPackage
                                    .addAllData(deviceScheduling)
                                    .build()

                            val packet = IncomingPacketOuterClass.IncomingPacket.newBuilder() // Monta o pacote final IncomingPacket
                                    .setId(time)
                                    .setDevScheduling(devSchedulingPackage)
                                    .build()

                            val payload: ByteArray = packet.toByteArray()
                            val crc2: Int = Utils.crc16(payload)
                            val crcBytes = byteArrayOf(
                                ((crc2 ushr 8) and 0xFF).toByte(),
                                (crc2 and 0xFF).toByte()
                            )
                            val finalPayload = payload + crcBytes

                            if (SharedData.codecWifiIsConnected.get() && SharedData.codecId.get() == identity) {
                                WebService.Request(
                                    identity,
                                    finalPayload,
                                    this@MainActivity,
                                    ::handleResponse
                                )
                                launch {
                                    delay(1000L)
                                    WebService.getCodecInfo(this@MainActivity, OutgoingPacketOuterClass.OutgoingPacket.PayloadCase.INFO.number.toByte(), ::handleResponse)
                                }
                            } else if (mqttManager.isConnected()) {
                                mqttManager.publish(topic, finalPayload)
                            }
                        }
                        return@launch
                    }

                }

                if (codec["last_automation_update"] as Long > codecUpdateOnMap[identity]?.automationId!!) { // Verifica se é necessário atualizar as automatizações do Codec
                    launch {
                        val time = codec["last_automation_update"] as Long
                        val levelPumpDeviceslist = dbHelper.getLevelPumpDevices(codec["idx"] as Int)
                        levelPumpDeviceslist.forEach { map ->
                            map["mk"] = 6
                            map["vl"] = 0
                        }

                        val automationDevices = levelPumpDeviceslist.map { item ->
                            Automation.AutomationData.newBuilder()
                                .setLevelIdx(item["li"] as Int)                // device_ord_idx -> level_idx
                                .setPumpIdx(item["pi"] as Int)                 // level_pump_ord_idx -> pump_idx
                                .setMask(item["mk"] as Int)                    // mk -> mask
                                .setValue(item["vl"] as Int)                   // vl -> value
                                .setWorkingTime(item["wt"] as Int)             // level_pump_working_time
                                .build()
                        }

                        val automationPackage = Automation.AutomationPackage.newBuilder() // Cria o AutomationPackage
                            .addAllData(automationDevices)
                            .build()

                        val packet = IncomingPacketOuterClass.IncomingPacket.newBuilder() // Monta o pacote final IncomingPacket
                            .setId(time)
                            .setAutomation(automationPackage)
                            .build()

                        val payload: ByteArray = packet.toByteArray()
                        val crc2: Int = Utils.crc16(payload)
                        val crcBytes = byteArrayOf(((crc2 ushr 8) and 0xFF).toByte(), (crc2 and 0xFF).toByte())
                        val finalPayload = payload + crcBytes

                        if (SharedData.codecWifiIsConnected.get() && SharedData.codecId.get() == identity) {
                            WebService.Request(identity, finalPayload, this@MainActivity, ::handleResponse)
                            launch {
                                delay(1000L)
                                WebService.getCodecInfo(this@MainActivity, OutgoingPacketOuterClass.OutgoingPacket.PayloadCase.INFO.number.toByte(), ::handleResponse)
                            }
                        } else if (mqttManager.isConnected()) {
                            mqttManager.publish(topic, finalPayload)
                        }
                    }
                    return@launch
                }

                if (codec["last_config_update"] as Long > codecUpdateOnMap[identity]?.configId!!) { // Verifica se é necessário atualizar as configurações do Codec
                    launch {
                        val time = codec["last_config_update"] as Long
                        val backwashCycle = sharedPref.getString("backwashCycle", "0")
                        val backwashDuration = sharedPref.getString("backwashDuration", "0")
                        val rainGaugeEnabled = sharedPref.getBoolean("rainGaugeEnabled", false)
                        val rainGaugeResolution = sharedPref.getString("rainGaugeResolution", "0")
                        val rainfallLimit = sharedPref.getString("rainfallLimit", "2")
                        val rainfallPauseDuration = sharedPref.getString("rainfallPauseDuration", "24")

                        val ssid = codec["wifi_ssid"] as String
                        val password = codec["wifi_passwd"] as String

                        val wifiCfg = Config.WifiConfig.newBuilder()
                            .setSsid(ssid)
                            .setPassword(password)
                            .build()

                        val cfgBuilder = Config.ConfigPackage.newBuilder()
                            .setBackwashCycle(backwashCycle.toString().toInt())
                            .setBackwashDuration(backwashDuration.toString().toInt())
                            .setBackwashDelay(30)
                            .setRaingaugeEnabled(rainGaugeEnabled)
                            .setRaingaugeFactor((1.0/rainGaugeResolution.toString().toFloat()).toInt())
                            .setRainfallLimit(rainfallLimit.toString().toInt())
                            .setRainfallPauseDuration(rainfallPauseDuration.toString().toInt())

                        if (SharedData.codecWifiIsConnected.get() && SharedData.codecId.get() == identity) {
                            cfgBuilder.setWifi(wifiCfg)
                        }

                        val cfg = cfgBuilder.build()

                        val packet = IncomingPacketOuterClass.IncomingPacket.newBuilder()
                            .setId(time)
                            .setConfig(cfg)
                            .build()

                        val payload: ByteArray = packet.toByteArray()
                        val crc2: Int = Utils.crc16(payload)
                        val crcBytes = byteArrayOf(((crc2 ushr 8) and 0xFF).toByte(), (crc2 and 0xFF).toByte())
                        val finalPayload = payload + crcBytes

                        println(finalPayload.joinToString(" ") { "%02X".format(it) })
                        //val received = Config.ConfigPackage.parseFrom(payload)

                        if (SharedData.codecWifiIsConnected.get() && SharedData.codecId.get() == identity) {
                            WebService.Request(identity, finalPayload, this@MainActivity, ::handleResponse)
                            launch {
                                delay(1000L)
                                WebService.getCodecInfo(this@MainActivity, OutgoingPacketOuterClass.OutgoingPacket.PayloadCase.INFO.number.toByte(), ::handleResponse)
                            }
                        } else if (mqttManager.isConnected()) {
                            mqttManager.publish(topic, finalPayload)
                        }
                    }
                    return@launch
                }
            } catch (e: Exception){
                e.printStackTrace()
            }
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        // Inflate the menu; this adds items to the action bar if it is present.
        menuInflater.inflate(R.menu.main, menu)
        return true
    }

    override fun onSupportNavigateUp(): Boolean {
        val navController = findNavController(R.id.nav_host_fragment_content_main)
        return navController.navigateUp(appBarConfiguration) || super.onSupportNavigateUp()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_settings -> {
                val navController = findNavController(R.id.nav_host_fragment_content_main)
                navController.navigate(R.id.nav_config, null, NavOptions.Builder()
                    .setLaunchSingleTop(true)
                    .build())
                true
            }
            R.id.action_about -> {
                fun PackageManager.getPackageInfoCompat(pkg: String, flags: Int = 0): PackageInfo = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    getPackageInfo(pkg, PackageManager.PackageInfoFlags.of(flags.toLong()))
                } else {
                    @Suppress("DEPRECATION") getPackageInfo(pkg, flags)
                }
                val pInfo = packageManager.getPackageInfoCompat(packageName)
                val versionName = pInfo.versionName

                AlertDialog.Builder(this)
                    .setTitle("Sobre")
                    .setMessage("IrrigaNet v$versionName")
                    .setPositiveButton("OK") { dialog, _ -> dialog.dismiss() }
                    .show()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    fun updateCodecList() {
        codecList = dbHelper.getAllCodecsOnMap()
    }

    fun getMqttManager(): HiveMqttManager {
        return mqttManager
    }

    fun getLatestCodecsUpdates(): Map<String, LatestCodecUpdates> = HashMap(codecUpdateOnMap)

    fun getMessageList(): Map<Pair<String, Int>, ByteArray> = HashMap(messageList)

    fun isWifiConnected(context: Context): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val network = connectivityManager.activeNetwork ?: return false
        val networkCapabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
        return networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)
    }

    fun isConnectedToWifi(context: Context): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val networks = connectivityManager.allNetworks
        for (network in networks) {
            val networkCapabilities = connectivityManager.getNetworkCapabilities(network)
            if (networkCapabilities != null && networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)) {
                return true
            }
        }
        return false
    }

    private fun showCodecDialog(identity: String) {
        val dialogBuilder = AlertDialog.Builder(this@MainActivity)
            .setTitle("Novo Codec Encontrado!")
            .setMessage("Deseja cadastrar este Codec?\n\nID: $identity")
            .setPositiveButton("OK") { dialog, _ ->
                val name = "Codec "+identity
                val idx = dbHelper.insertCodec(identity, name, "", "").toInt()
                dialog.dismiss()
            }
            .setNegativeButton("Cancel") { dialog, _ ->
                dialog.cancel()
            }

        val alertDialog = dialogBuilder.create()
        alertDialog.show()
    }

    fun saveMessageList() {
        val bos = ByteArrayOutputStream()
        ObjectOutputStream(bos).use { oos ->
            oos.writeObject(messageList)
            oos.flush()
        }

        with(sharedPref.edit()) {
            putString("message_list_b64", Base64.encodeToString(bos.toByteArray(), Base64.NO_WRAP))   // salva
            apply()
        }
    }

    fun loadMessageList() {
        val b64 = sharedPref.getString("message_list_b64", null)?: return

        val bytes = Base64.decode(b64, Base64.NO_WRAP)
        val obj   = ObjectInputStream(ByteArrayInputStream(bytes)).use { ois ->
            @Suppress("UNCHECKED_CAST")
            ois.readObject() as MutableMap<Pair<String, Int>, ByteArray>
        }
        messageList.clear()
        messageList.putAll(obj)
    }

    fun handleResponse(identity: String?, response: ByteArray?) {
        try {
            if (response == null) {
                Log.w("CallbackHandler", "Received null response")
                return
            }

            var codecIdentity = identity?:""
            if(SharedData.codecWifiIsConnected.get()){
                codecIdentity = SharedData.codecId.get().toString()
            }

            val packet = OutgoingPacketOuterClass.OutgoingPacket.parseFrom(response)

            when (packet.payloadCase) {
                OutgoingPacketOuterClass.OutgoingPacket.PayloadCase.INFO -> { // Verifica se é um INFO
                    val infoPackage = packet.info

                    if(identity == null){
                        codecIdentity = infoPackage.codecId
                        SharedData.codecWifiIsConnected.set(true)
                    }

                    if (SharedData.codecId.get().isNullOrEmpty() && codecIdentity.isNotBlank() && !dbHelper.existsCodecIdentity(codecIdentity)) {
                        runOnUiThread { showCodecDialog(codecIdentity) }
                    }

                    SharedData.codecId.set(codecIdentity)

                    val codecFirmware = infoPackage.firmwareEsp.toInt()
                    val pauseScheduling = infoPackage.schedulingPaused.toInt() == 1

                    val identityKey = codecIdentity ?: ""
                    if(!codecUpdateOnMap.containsKey(identityKey)){
                        codecUpdateOnMap[codecIdentity?:""] = LatestCodecUpdates(
                            codecFirmware = codecFirmware,
                            pauseScheduling = pauseScheduling,
                            schedulingRunning = infoPackage.schedulingRunning.toInt() == 1,
                            devicesId = infoPackage.devicesId.toLong(),
                            schedulingId = infoPackage.schedulingId.toLong(),
                            devSchedulingId = infoPackage.devSchedulingId.toLong(),
                            automationId = infoPackage.automationId.toLong(),
                            configId = infoPackage.configId.toLong(),
                            lastInfo = System.currentTimeMillis() / 1000,
                            syncBitmask = null,
                            onBitmask = null,
                            inputBitmask = null,
                            failedBitmask = null,
                            rainStatus = null,
                            rainfall = null,
                            false
                        )
                    } else {
                        val updated = codecUpdateOnMap[codecIdentity ?: ""]?.copy(
                            codecFirmware = codecFirmware,
                            pauseScheduling = pauseScheduling,
                            schedulingRunning = infoPackage.schedulingRunning.toInt() == 1,
                            devicesId = infoPackage.devicesId.toLong(),
                            schedulingId = infoPackage.schedulingId.toLong(),
                            devSchedulingId = infoPackage.devSchedulingId.toLong(),
                            automationId = infoPackage.automationId.toLong(),
                            configId = infoPackage.configId.toLong(),
                            lastInfo = System.currentTimeMillis() / 1000,
                        )

                        if (updated != null) {
                            codecUpdateOnMap[codecIdentity] = updated
                        }
                    }
                }

                OutgoingPacketOuterClass.OutgoingPacket.PayloadCase.STATUS -> {
                    val statusPackage = packet.status

                    val pauseScheduling = statusPackage.schedulingPaused.toInt() == 1

                    val updated = codecUpdateOnMap[codecIdentity]?.copy(
                        pauseScheduling = pauseScheduling,
                        schedulingRunning = statusPackage.schedulingRunning.toInt() == 1,
                        lastInfo = System.currentTimeMillis() / 1000,
                        syncBitmask = statusPackage.syncBitmask.toLong(),
                        onBitmask = statusPackage.onBitmask.toLong(),
                        inputBitmask = statusPackage.inputBitmask.toLong(),
                        failedBitmask = statusPackage.failedBitmask.toLong()
                    )

                    if(statusPackage.hasRainfall()){
                        updated?.rainStatus = statusPackage.raining == 1
                        updated?.rainfall = statusPackage.rainfall
                    }

                    if (updated != null && codecIdentity.isNotBlank()) {
                        codecUpdateOnMap[codecIdentity] = updated
                    }

                }

                OutgoingPacketOuterClass.OutgoingPacket.PayloadCase.SCHEDULING_REPORT -> {
                    //mqttViewModel.onMessageArrived(identity.toString(), packet.toByteArray())
                    messageList[identity.toString() to OutgoingPacketOuterClass.OutgoingPacket.PayloadCase.SCHEDULING_REPORT.number] = packet.toByteArray()
                    saveMessageList()
                }

                OutgoingPacketOuterClass.OutgoingPacket.PayloadCase.AUTOMATION_REPORT -> {
                    messageList[identity.toString() to OutgoingPacketOuterClass.OutgoingPacket.PayloadCase.AUTOMATION_REPORT.number] = packet.toByteArray()
                    saveMessageList()
                }

                else -> {
                    Log.d("CallbackHandler", "Unknown payload type received")
                }
            }

        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}