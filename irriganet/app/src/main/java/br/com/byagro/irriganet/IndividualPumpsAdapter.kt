package br.com.byagro.irriganet

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView

class IndividualPumpsAdapter(
    private val pumpsList: MutableList<DeviceItem>,
    private val onItemClick: (DeviceItem) -> Unit
) :
    RecyclerView.Adapter<IndividualPumpsAdapter.IndividualPumpsViewHolder>() {

    class IndividualPumpsViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val textView: TextView = itemView.findViewById(R.id.item_text)

        fun bind(item: DeviceItem, onItemClick: (DeviceItem) -> Unit) {
            itemView.setOnClickListener {
                onItemClick(item)
            }

        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): IndividualPumpsViewHolder {
        val itemView = LayoutInflater.from(parent.context).inflate(R.layout.item_model_simple_text, parent, false)
        return IndividualPumpsViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: IndividualPumpsViewHolder, position: Int) {
        val currentGroup = pumpsList[position]
        holder.textView.text = currentGroup.name
        holder.bind(currentGroup, onItemClick)
    }

    override fun getItemCount() = pumpsList.size
}