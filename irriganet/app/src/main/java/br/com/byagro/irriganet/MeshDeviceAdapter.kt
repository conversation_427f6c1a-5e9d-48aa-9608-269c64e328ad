package br.com.byagro.irriganet

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView

class MeshDeviceAdapter(
    private val MeshDeviceList: List<MeshDeviceItem>,
    private val onItemClick: (MeshDeviceItem) -> Unit,
    private val onDeleteClick: (Int) -> Unit
) :
    RecyclerView.Adapter<MeshDeviceAdapter.MeshDeviceViewHolder>() {

    class MeshDeviceViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val textView: TextView = itemView.findViewById(R.id.item_text)
        val deleteButton: ImageButton = itemView.findViewById(R.id.btn_delete)

        fun bind(item: MeshDeviceItem, onItemClick: (MeshDeviceItem) -> Unit, onDeleteClick: (Int) -> Unit) {
            itemView.setOnClickListener {
                onItemClick(item)
            }

            deleteButton.setOnClickListener {
                onDeleteClick(adapterPosition)
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MeshDeviceViewHolder {
        val itemView = LayoutInflater.from(parent.context).inflate(R.layout.item_model_text_btn, parent, false)
        return MeshDeviceViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: MeshDeviceViewHolder, position: Int) {
        val currentMeshDevice = MeshDeviceList[position]
        holder.textView.text = currentMeshDevice.label
        holder.bind(currentMeshDevice, onItemClick, onDeleteClick)
    }

    override fun getItemCount() = MeshDeviceList.size
}