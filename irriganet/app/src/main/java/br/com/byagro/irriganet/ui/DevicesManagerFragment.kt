package br.com.byagro.irriganet.ui

import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.RecyclerView
import br.com.byagro.irriganet.DeviceAdapter
import br.com.byagro.irriganet.R
import androidx.recyclerview.widget.LinearLayoutManager
import br.com.byagro.irriganet.DBHelper
import br.com.byagro.irriganet.DeviceItem
import br.com.byagro.irriganet.IndividualPumpsAdapter
import br.com.byagro.irriganet.MainActivity
import br.com.byagro.irriganet.SimpleItem
import br.com.byagro.irriganet.databinding.FragmentDevicesManagerBinding

// TODO: Rename parameter arguments, choose names that match
// the fragment initialization parameters, e.g. ARG_ITEM_NUMBER
private const val ARG_PARAM1 = "param1"
private const val ARG_PARAM2 = "param2"

/**
 * A simple [Fragment] subclass.
 * Use the [DevicesManagerFragment.newInstance] factory method to
 * create an instance of this fragment.
 */
class DevicesManagerFragment : Fragment() {
    private lateinit var binding: FragmentDevicesManagerBinding
    private lateinit var dbHelper: DBHelper

    private lateinit var adapter: DeviceAdapter
    private var deviceList: MutableList<DeviceItem> = mutableListOf()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {

        }
        dbHelper = DBHelper(requireContext())
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentDevicesManagerBinding.inflate(inflater, container, false)

        try {
            binding.fragDevicesManagerRecyclerViewDevices.layoutManager =
                LinearLayoutManager(context)

            adapter = DeviceAdapter(deviceList, { selectedItem -> onItemClick(selectedItem) })
            binding.fragDevicesManagerRecyclerViewDevices.adapter = adapter

            val act = activity as MainActivity

            deviceList.clear()
            deviceList.addAll(dbHelper.getAllDevicesWithMeshInfo())

            deviceList.forEach { device ->
                val identity = device.codecIdentity              // ajuste conforme sua chave real
                val updates  = act.getLatestCodecsUpdates()[identity] ?: return@forEach   // nada encontrado -> pula

                val mask = 1L shl device.ordIdx

                val transmitting = updates.syncBitmask?.let { (it and mask) != 0L }
                val poweredOn    = updates.onBitmask?.let  { (it and mask) != 0L }

                device.sync = transmitting
                device.on   = poweredOn
            }

            adapter.notifyDataSetChanged()
        } catch (e: Exception) {
            e.printStackTrace()
        }

        return binding.root
    }

    private fun onItemClick(Item: DeviceItem) {
        val bundle = Bundle().apply {
            putInt("deviceIdx", Item.idx)
        }
        findNavController().navigate(R.id.action_nav_devices_manager_to_nav_device_control, bundle)
    }

}