syntax = "proto3";

package codec.in.devices;

message DevicesData {
  int32 idx              = 1;  // index in the array (device slot)
  int32 mesh_id          = 2;  // mesh ID
  int32 device_id        = 3;  // device ID
  int32 device_type      = 4;  // device type
  int32 out1             = 5;  // output 1 state
  int32 out2             = 6;  // output 2 state
  int32 input            = 7;  // input state
  int32 mode             = 8;  // operating mode
  int32 sector           = 9;  // sector ID
  int32 group_idx        = 10; // group index
  int32 eqpt_ver         = 11; // equipment version
}

message DevicesPackage {
  repeated DevicesData data = 1;      // list of mesh devices
}
