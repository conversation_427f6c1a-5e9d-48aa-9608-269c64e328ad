syntax = "proto3";

package codec.in.automation;

message AutomationData {
  int32 level_idx = 1;     // level input (sensor de nível)
  int32 pump_idx = 2;      // pump index
  int32 mask = 3;          // mascara
  int32 value = 4;         // valor de acionamento
  int32 working_time = 5;  // working time (tempo de acionamento)
}

message AutomationPackage {
  repeated AutomationData data = 1;        // Lista de acionamentos
}
