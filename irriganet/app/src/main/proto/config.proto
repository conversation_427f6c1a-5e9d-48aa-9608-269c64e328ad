syntax = "proto3";

package codec.in.config;

message WifiConfig {
  string ssid     = 1;                      // Wi-Fi SSID
  string password = 2;                      // Wi-Fi password
}

message MeshConfig {
  bytes  key     = 1;                       // Chave da criptografia da rede Mesh
  uint32 channel = 2;                       // Canal da rede Mesh
}

message ConfigPackage {
  int32 backwash_cycle = 1;                 // número do ciclo de retrolavagem
  int32 backwash_duration = 2;              // duração da retrolavagem
  int32 backwash_delay = 3;                 // atraso antes da retrolavagem
  bool  raingauge_enabled = 4;              // sensor de chuva ativado
  int32 raingauge_factor = 5;               // resolução do pluviômetro
  int32 rainfall_limit = 6;                 // limite de chuva
  int32 rainfall_pause_duration = 7;        // tempo de pausa após chuva
  WifiConfig wifi = 8;                      // Configuração do Wi-Fi
  MeshConfig mesh = 9;                      // Configuração da rede Mesh
}
