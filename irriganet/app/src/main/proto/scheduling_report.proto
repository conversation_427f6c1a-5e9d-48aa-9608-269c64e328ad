syntax = "proto3";

package codec.out.scheduling_report;

message SchedulingReportData {
  int32 scheduling_idx = 1;     // Índice do agendamento
  uint64 start_time = 2;        // Timestamp do início do agendamento
  uint64 end_time = 3;          // Timestamp do fim do agendamento
  uint64 sector_bitmask1 = 4;   // Bitmask de setores acionados, mascara com 64bits iniciais
  uint64 sector_bitmask2 = 5;   // Bitmask de setores acionados, mascara com mais 64bits
  uint64 ferti_bitmask1 = 6;    // Bitmask da ferti de setores acionados, mascara com 64bits iniciais
  uint64 ferti_bitmask2 = 7;    // Bitmask da ferti de setores acionados, mascara com mais 64bits
  uint32 waterpump = 8;         // Estado da bomba de água
  uint32 backwash = 9;          // Estado da retrolavagem
  uint64 backwash_time = 10;    // Timestamp de início da retrolavagem
  int32 status = 11;            // Código de status do agendamento
}

message SchedulingReportPackage {
  repeated SchedulingReportData data = 1;     // Lista de relatórios
}
