<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.PumpControlFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="10dp">

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/frag_pump_control_text_layout_working_time"
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            app:boxStrokeColor="@color/black"
            app:hintTextAppearance="@style/CollapsedHintTextAppearance"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/frag_pump_control_text_working_time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                android:digits="0123456789"
                android:hint="Duração"
                android:inputType="numberDecimal" />
        </com.google.android.material.textfield.TextInputLayout>

        <Button
            android:id="@+id/frag_pump_control_button_turn_on"
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:text="LIGAR"
            android:layout_marginTop="20dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/frag_pump_control_text_layout_working_time"/>

        <Button
            android:id="@+id/frag_pump_control_button_turn_off"
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="DESLIGAR"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/frag_pump_control_button_turn_on" />

        <Switch
            android:id="@+id/frag_pump_control_switch_auto"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="Automático"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.351"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/frag_pump_control_button_turn_off" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>