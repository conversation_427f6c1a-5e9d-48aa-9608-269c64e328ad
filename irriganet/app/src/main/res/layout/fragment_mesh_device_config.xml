<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.MeshDeviceFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:fillViewport="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:layout_marginStart="20dp"
                android:layout_marginEnd="20dp">

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:hint="Nome do Dispositivo">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/frag_mesh_device_mesh_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@android:color/transparent"
                        android:inputType="textCapSentences" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:hint="Mesh Id">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/frag_mesh_device_mesh_id"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@android:color/transparent"
                        android:digits="0123456789ABCDEF"
                        android:inputType="textCapCharacters" />
                </com.google.android.material.textfield.TextInputLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:text="Tipo de dispositivo"
                        android:textStyle="bold" />

                    <RadioGroup
                        android:id="@+id/frag_mesh_device_type_group"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:paddingTop="0dp">

                        <RadioButton
                            android:id="@+id/frag_mesh_device_radio_valve"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true"
                            android:text="Acionador de válvulas" />

                        <RadioButton
                            android:id="@+id/frag_mesh_device_radio_pump"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Bomba d'água" />

                        <RadioButton
                            android:id="@+id/frag_mesh_device_radio_level"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Controle de nível" />
                    </RadioGroup>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/frag_mesh_device_linear_layout_pump_devices"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="visible">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Controlador da Bomba"
                        android:textStyle="bold" />

                    <RadioGroup
                        android:id="@+id/frag_mesh_device_equipament_group"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:paddingTop="0dp">

                        <RadioButton
                            android:id="@+id/frag_mesh_device_radio_pl10"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true"
                            android:text="PL10" />

                        <RadioButton
                            android:id="@+id/frag_mesh_device_radio_pl50"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="5dp"
                            android:text="PL50" />

                    </RadioGroup>

                    <LinearLayout
                        android:id="@+id/frag_mesh_device_linear_layout_pump_mode"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="visible">
                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Modo"
                            android:textStyle="bold" />
                        <RadioGroup
                            android:id="@+id/frag_mesh_device_mode_group"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:paddingTop="0dp">

                            <RadioButton
                                android:id="@+id/frag_mesh_device_radio_continue"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:checked="true"
                                android:text="CONTINUO" />

                            <RadioButton
                                android:id="@+id/frag_mesh_device_radio_pulse"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="5dp"
                                android:text="PULSO" />

                        </RadioGroup>
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/frag_mesh_device_linear_layout_pump_devs"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="visible">
                        <Switch
                            android:id="@+id/frag_mesh_device_switch_individual_pump"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:layout_marginStart="5dp"
                            android:text="Bomba de Serviço" />

                        <Switch
                            android:id="@+id/frag_mesh_device_switch_irrigation_pump"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:layout_marginStart="5dp"
                            android:text="Bomba de Irrigação" />

                        <Switch
                            android:id="@+id/frag_mesh_device_switch_ferti"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:layout_marginStart="5dp"
                            android:text="Ferti" />

                        <Switch
                            android:id="@+id/frag_mesh_device_switch_backwash"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:layout_marginStart="5dp"
                            android:text="Retrolavagem" />

                        <Switch
                            android:id="@+id/frag_mesh_device_switch_check_input"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:layout_marginStart="5dp"
                            android:text="Monitorar" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/frag_mesh_device_linear_layout_outputs"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Válvulas"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <CheckBox
                                android:id="@+id/frag_mesh_device_check_box1"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="V1" />

                            <EditText
                                android:id="@+id/frag_mesh_device_edit_text_sector_s1"
                                android:layout_width="80dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="5dp"
                                android:digits="0123456789"
                                android:hint="Setor"
                                android:inputType="numberDecimal" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <CheckBox
                                android:id="@+id/frag_mesh_device_check_box2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="V2" />

                            <EditText
                                android:id="@+id/frag_mesh_device_edit_text_sector_s2"
                                android:layout_width="80dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="5dp"
                                android:digits="0123456789"
                                android:hint="Setor"
                                android:inputType="numberDecimal" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <CheckBox
                                android:id="@+id/frag_mesh_device_check_box3"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="V3" />

                            <EditText
                                android:id="@+id/frag_mesh_device_edit_text_sector_s3"
                                android:layout_width="80dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="5dp"
                                android:digits="0123456789"
                                android:hint="Setor"
                                android:inputType="numberDecimal" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <CheckBox
                                android:id="@+id/frag_mesh_device_check_box4"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="V4" />

                            <EditText
                                android:id="@+id/frag_mesh_device_edit_text_sector_s4"
                                android:layout_width="80dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="5dp"
                                android:digits="0123456789"
                                android:hint="Setor"
                                android:inputType="numberDecimal" />
                        </LinearLayout>

                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/frag_mesh_device_linear_layout_individual_pumps"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="visible">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:text="Bombas"
                        android:textStyle="bold" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="0dp"
                        android:hint="Tempo de segurança">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/frag_mesh_device_level_pump_working_time"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@android:color/transparent"
                            android:digits="0123456789"
                            android:inputType="numberDecimal" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/frag_mesh_device_recycler_view"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        tools:listitem="@layout/item_model_level_pump" />
                </LinearLayout>

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

    </LinearLayout>

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/frag_mesh_device_fab_save"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end|bottom"
        android:layout_margin="16dp"
        android:contentDescription="Salvar"
        app:srcCompat="@android:drawable/ic_menu_save" />

</FrameLayout>
