<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.CodecConfigFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="10dp">

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/frag_codec_config_text_input_layout_name"
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            app:boxStrokeColor="@color/black"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/frag_codec_config_text_input_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                android:hint="Nome do Codec"
                android:inputType="textCapSentences" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/frag_codec_config_text_input_layout_identity"
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            app:boxStrokeColor="@color/black"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/frag_codec_config_text_input_layout_name">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/frag_codec_config_text_input_identity"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                android:hint="ID do Codec"
                android:digits="0123456789ABCDEF"
                android:inputType="textCapCharacters" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/frag_codec_config_text_input_layout_wifi_ssid"
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            app:boxStrokeColor="@color/black"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/frag_codec_config_text_input_layout_identity">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/frag_codec_config_text_input_wifi_ssid"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                android:hint="SSID do Wi-Fi"
                android:inputType="text" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/frag_codec_config_text_input_layout_wifi_passwd"
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            app:boxStrokeColor="@color/black"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/frag_codec_config_text_input_layout_wifi_ssid">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/frag_codec_config_text_input_wifi_passwd"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                android:hint="Senha do Wi-Fi"
                android:inputType="text" />
        </com.google.android.material.textfield.TextInputLayout>

        <Switch
            android:id="@+id/frag_codec_config_switch_enabled"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:checked="true"
            android:text="Habilitado"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.34"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/frag_codec_config_text_input_layout_wifi_passwd" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/frag_codec_config_fab_delete_codec"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginBottom="96dp"
        android:layout_marginEnd="16dp"
        android:contentDescription="Apagar Codec"
        app:srcCompat="@android:drawable/ic_menu_delete"
        app:tint="@android:color/white" />

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/frag_codec_config_fab_save_codec"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginBottom="24dp"
        android:layout_marginEnd="16dp"
        android:contentDescription="Salvar Codec"
        app:srcCompat="@android:drawable/ic_menu_save"
        app:tint="@android:color/white" />

</FrameLayout>
