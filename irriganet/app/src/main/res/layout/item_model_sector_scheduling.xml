<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp"
        android:gravity="center_vertical">

        <CheckBox
            android:id="@+id/checkBoxSetor"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Setor"/>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/textInputLayoutDuration"
            android:layout_width="70dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            app:boxStrokeColor="@color/black"
            app:hintTextAppearance="@style/CollapsedHintTextAppearance">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/textInputDuration"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                android:hint="Duração"
                android:digits="0123456789"
                android:inputType="numberDecimal" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/textInputLayoutFerti"
            android:layout_width="70dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            app:boxStrokeColor="@color/black">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/textInputFerti"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                android:hint="Ferti"
                android:digits="0123456789"
                android:inputType="numberDecimal" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/textInputLayoutFertiDelay"
            android:layout_width="70dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            app:boxStrokeColor="@color/black">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/textInputFertiDelay"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                android:hint="Atraso"
                android:digits="0123456789"
                android:inputType="numberDecimal" />
        </com.google.android.material.textfield.TextInputLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>