<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F7F8FC"
    tools:context=".ui.ReportFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp">

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/status_icon_sidebar"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/metrics_column"
            app:layout_constraintBottom_toBottomOf="@id/metrics_column"
            app:cardCornerRadius="12dp"
            android:layout_marginBottom="1dp"
            app:cardElevation="1dp"
            app:strokeWidth="0dp">

            <LinearLayout
                android:layout_width="55dp"
                android:layout_height="match_parent"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                android:padding="3dp">

                <ImageView
                    android:id="@+id/frag_report_image_view_sync_status"
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:layout_marginTop="10dp"
                    android:background="@drawable/status_icon_background_neutral"
                    android:contentDescription="Estado do Sincronismo"
                    android:padding="4dp"
                    android:src="@drawable/baseline_sync_24" />

                <ImageView
                    android:id="@+id/frag_report_image_view_paused_status"
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:layout_marginTop="10dp"
                    android:background="@drawable/status_icon_background_neutral"
                    android:contentDescription="Estado da Pausa"
                    android:padding="4dp"
                    android:src="@drawable/baseline_pause_circle_outline_24"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <LinearLayout
                    android:id="@+id/frag_report_linear_layout_rain_status_container"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <ImageView
                        android:id="@+id/frag_report_image_view_rain_status"
                        android:layout_width="28dp"
                        android:layout_height="28dp"
                        android:background="@drawable/status_icon_background_neutral"
                        android:contentDescription="Status da Chuva"
                        android:padding="4dp"
                        android:src="@drawable/baseline_water_drop_24"
                        app:tint="@color/black" />

                    <TextView
                        android:id="@+id/frag_report_text_view_rain_amount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
                        android:textColor="@color/black"
                        android:textStyle="bold"
                        tools:text="15.2mm" />
                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <LinearLayout
            android:id="@+id/metrics_column"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:orientation="vertical"
            app:layout_constraintEnd_toStartOf="@id/status_icon_sidebar"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <GridLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:columnCount="2"
                android:clipToPadding="false"
                android:padding="1dp">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_columnWeight="1"
                    android:layout_marginEnd="6dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="2dp"
                    app:strokeWidth="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="8dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Codec(s)"
                            android:layout_marginBottom="8dp"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"/>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical">

                            <ImageView
                                android:id="@+id/frag_report_image_view_codecs"
                                android:layout_width="30dp"
                                android:layout_height="30dp"
                                android:src="@drawable/ic_codec"
                                app:tint="@color/black"/>

                            <TextView
                                android:id="@+id/frag_report_text_view_codecs"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="12dp"
                                android:textAppearance="@style/TextAppearance.MaterialComponents.Headline5"
                                android:textColor="?android:attr/textColorPrimary"
                                tools:text="2" />
                        </LinearLayout>
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_columnWeight="1"
                    android:layout_marginStart="6dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="2dp"
                    app:strokeWidth="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="8dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Dispositivos"
                            android:layout_marginBottom="8dp"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"/>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical">
                            <ImageView
                                android:id="@+id/frag_report_image_view_devices"
                                android:layout_width="28dp"
                                android:layout_height="28dp"
                                android:src="@drawable/ic_dispositivos"
                                app:tint="@color/black"/>

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:layout_marginStart="8dp">

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:gravity="center_vertical"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:id="@+id/frag_report_text_view_synced_devices_count"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                                        android:textStyle="bold"
                                        android:textColor="?android:attr/textColorPrimary"
                                        tools:text="49"/>

                                    <TextView
                                        android:id="@+id/frag_report_text_view_synced_devices_total"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="4dp"
                                        android:text="/ 50"
                                        android:textAppearance="@style/TextAppearance.MaterialComponents.Caption" />
                                </LinearLayout>
                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Sincron.."
                                    android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"/>
                            </LinearLayout>
                        </LinearLayout>
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

            </GridLayout>

            <GridLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                android:columnCount="2"
                android:clipToPadding="false"
                android:padding="1dp">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_columnWeight="1"
                    android:layout_marginEnd="6dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="2dp"
                    app:strokeWidth="0dp"
                    android:layout_marginBottom="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="vertical"
                        android:padding="8dp">

                        <TextView
                            android:id="@+id/frag_report_text_view_groups_title"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Talhões"
                            android:layout_marginBottom="8dp"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"/>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical">
                            <ImageView
                                android:id="@+id/frag_report_image_view_groups"
                                android:layout_width="28dp"
                                android:layout_height="28dp"
                                android:src="@drawable/baseline_map_24" />

                            <TextView
                                android:id="@+id/frag_report_text_view_groups"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="12dp"
                                android:textAppearance="@style/TextAppearance.MaterialComponents.Headline5"
                                android:textColor="?android:attr/textColorPrimary"
                                tools:text="8" />
                        </LinearLayout>
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_columnWeight="1"
                    android:layout_marginStart="6dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="2dp"
                    app:strokeWidth="0dp"
                    android:layout_marginBottom="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="vertical"
                        android:padding="8dp">

                        <TextView
                            android:id="@+id/frag_report_text_view_sectors_title"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Setores"
                            android:layout_marginBottom="8dp"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"/>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical">
                            <ImageView
                                android:id="@+id/frag_report_image_view_sectors"
                                android:layout_width="28dp"
                                android:layout_height="28dp"
                                android:src="@drawable/ic_aspersor"/>

                            <TextView
                                android:id="@+id/frag_report_text_view_sectors"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="12dp"
                                android:textAppearance="@style/TextAppearance.MaterialComponents.Headline5"
                                android:textColor="?android:attr/textColorPrimary"
                                tools:text="16" />
                        </LinearLayout>
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>
            </GridLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/frag_report_linear_layout_report"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="10dp"
            android:orientation="vertical"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/metrics_column"
            tools:visibility="visible">

            <TextView
                android:id="@+id/frag_report_text_view_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginBottom="2dp"
                android:text="Atividades Recentes"
                android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/frag_report_recycler_report"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:clipToPadding="false"
                android:paddingBottom="16dp"
                tools:itemCount="3"
                tools:listitem="@layout/item_report" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
