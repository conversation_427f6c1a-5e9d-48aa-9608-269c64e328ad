import com.google.protobuf.gradle.*

plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    id 'com.google.protobuf' version '0.9.5'
}

android {
    namespace 'br.com.byagro.irriganet'
    compileSdk 35

    defaultConfig {
        applicationId "br.com.byagro.irriganet"
        minSdk 24
        targetSdk 35
        versionCode 6
        versionName "1.5"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    def userHome = System.properties['user.home']
    File fileKeyStore = userHome ? file("$userHome/geocontrol/android-geocontrol.p12") : null
    println("fileKeyStoreDev-$fileKeyStore")
    signingConfigs {
        release {
            //Constroi assinado ou não
            if (fileKeyStore?.canRead()) {
                storeFile fileKeyStore
                storePassword "Geo2018control25"
                keyAlias "geocontrol"
                keyPassword "Geo2018control25"
            }
        }
    }

    packagingOptions {
        exclude 'META-INF/INDEX.LIST'
        exclude 'META-INF/io.netty.versions.properties'
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'

            //Se puder ler a chave ele vai gerar o apk assinado com a chave da geocontrol, no momento apenas o jenkins tem acesso a chave da geocontrol
            if (fileKeyStore?.canRead()) {
                signingConfig signingConfigs.release
                applicationVariants.all { variant ->
                    variant.outputs.all {
                        outputFileName = "irriganet-android-${defaultConfig.versionName}.apk"
                    }
                }
            }
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = '11'
    }
    buildFeatures {
        viewBinding true
    }
}

dependencies {
    implementation libs.androidx.core.ktx
    implementation libs.androidx.appcompat
    implementation libs.material
    implementation libs.androidx.constraintlayout
    implementation libs.androidx.lifecycle.livedata.ktx
    implementation libs.androidx.lifecycle.viewmodel.ktx
    implementation libs.androidx.navigation.fragment.ktx
    implementation libs.androidx.navigation.ui.ktx
    implementation("com.hivemq:hivemq-mqtt-client:1.3.7")
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'com.google.code.gson:gson:2.8.9'
    implementation 'com.android.volley:volley:1.2.1'
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.8.0")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.0")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-jdk8:1.8.0")
    implementation("org.jetbrains.kotlinx:kotlinx-serialization-core:1.8.1")
    implementation("org.jetbrains.kotlinx:kotlinx-serialization-cbor:1.8.1")
    implementation("com.upokecenter:cbor:4.5.2")
    implementation 'com.google.protobuf:protobuf-kotlin-lite:3.25.3'
    implementation "com.google.android.material:material:1.12.0"
    testImplementation libs.junit
    androidTestImplementation libs.androidx.junit
    androidTestImplementation libs.androidx.espresso.core
}

protobuf {
    protoc { artifact = "com.google.protobuf:protoc:3.25.3" }

    generateProtoTasks {
        all().each { task ->
            task.builtins {
                //kotlin { option 'lite' }     // <- gera código Kotlin
                java { option 'lite' }    // se também quiser Java
            }
        }
    }
}