import db from "../connection";

/**
 * Gets the current property for a device based on the reference date
 * @param deviceIdentifier The identifier of the device
 * @param deviceModel The model of the device (e.g., 'LIC')
 * @param referenceDate The date to check for active property association
 * @returns The property record if found, undefined otherwise
 */
export async function getCurrentPropertyForDevice(
  deviceIdentifier: string,
  deviceModel: string,
  referenceDate: Date = new Date()
) {
  const propertyResult = await db`
    SELECT p.*
    FROM property p
    JOIN property_device pd ON p.id = pd.property
    JOIN device d ON pd.device = d.id
    WHERE d.identifier = ${deviceIdentifier}
      AND d.model = ${deviceModel}
      AND ${referenceDate} >= COALESCE(pd.start_date, '-infinity'::timestamp)
      AND ${referenceDate} <= COALESCE(pd.end_date, 'infinity'::timestamp)
  `;

  return propertyResult.length > 0 ? propertyResult[0] : undefined;
}

/**
 * Gets device metadata for a device based on the reference date
 * @param deviceIdentifier The identifier of the device
 * @param deviceModel The model of the device (e.g., 'LIC')
 * @param referenceDate The date to check for active property association
 * @returns The device metadata if found, undefined otherwise
 */
export async function getDeviceMetadataForProperty(
  deviceIdentifier: string,
  deviceModel: string,
  referenceDate: Date = new Date()
) {
  const deviceResult = await db`
    SELECT d.metadata
    FROM device d
    JOIN property_device pd ON d.id = pd.device
    WHERE d.identifier = ${deviceIdentifier}
      AND d.model = ${deviceModel}
      AND ${referenceDate} >= COALESCE(pd.start_date, '-infinity'::timestamp)
      AND ${referenceDate} <= COALESCE(pd.end_date, 'infinity'::timestamp)
  `;

  return deviceResult.length > 0 ? deviceResult[0].metadata : undefined;
}

/**
 * Gets property_device metadata for a device based on the reference date
 * @param deviceIdentifier The identifier of the device
 * @param deviceModel The model of the device (e.g., 'LIC')
 * @param referenceDate The date to check for active property association
 * @returns The property_device metadata if found, undefined otherwise
 */
export async function getPropertyDeviceMetadataForDevice(
  deviceIdentifier: string,
  deviceModel: string,
  referenceDate: Date = new Date()
) {
  const propertyDeviceResult = await db`
    SELECT pd.metadata
    FROM property_device pd
    JOIN device d ON pd.device = d.id
    WHERE d.identifier = ${deviceIdentifier}
      AND d.model = ${deviceModel}
      AND ${referenceDate} >= COALESCE(pd.start_date, '-infinity'::timestamp)
      AND ${referenceDate} <= COALESCE(pd.end_date, 'infinity'::timestamp)
  `;

  return propertyDeviceResult.length > 0
    ? propertyDeviceResult[0].metadata
    : undefined;
}
