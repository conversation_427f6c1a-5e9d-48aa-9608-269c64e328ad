import { type TransactionSQL } from "bun";
import { randomUUID } from "crypto";

// --- Entity interfaces based on DDL ---
export interface Account {
  id: string;
  owner: string;
  date_created: string;
  user_created: string | null;
  date_updated: string;
  user_updated: string | null;
  metadata: any | null;
  notes: string | null;
}

export interface Property {
  id: string;
  account: string;
  name: string;
  backwash_duration_minutes: number | null;
  backwash_period_minutes: number | null;
  backwash_delay_seconds: number | null;
  rain_gauge_enabled: boolean;
  rain_gauge_resolution_mm: number;
  precipitation_volume_limit_mm: number;
  precipitation_suspended_duration_hours: number;
  timezone: string;
  point: any | null;
  address_postal_code: string | null;
  address_street_name: string | null;
  address_street_number: string | null;
  address_complement: string | null;
  address_neighborhood: string | null;
  address_city: string | null;
  address_state: string | null;
  address_country: string;
  notes: string | null;
  date_created: string;
  user_created: string | null;
  date_updated: string;
  user_updated: string | null;
  metadata: any | null;
}

export interface Device {
  id: string;
  identifier: string;
  model: "LIC" | "WPC-PL10" | "WPC-PL50" | "VC" | "RM";
  date_created: string;
  user_created: string | null;
  date_updated: string;
  user_updated: string | null;
  metadata: any | null;
  notes: string | null;
}

export interface PropertyDevice {
  id: string;
  device: string;
  property: string;
  start_date: string;
  end_date: string | null;
  current_mesh_device_mapping: string | null;
  date_created: string;
  user_created: string | null;
  date_updated: string;
  user_updated: string | null;
  metadata: any | null;
  notes: string | null;
}

export interface MeshDeviceMapping {
  id: string;
  mesh_property_device: string;
  lic_property_device: string;
  start_date: string;
  end_date: string | null;
  date_created: string;
  user_created: string | null;
  date_updated: string;
  user_updated: string | null;
}

export interface DirectusUser {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  password: string;
  status: string;
  provider: string;
  role: string | null;
}

export async function insertUser(
  trx: TransactionSQL,
  overrides: Partial<DirectusUser> = {}
): Promise<DirectusUser> {
  const id = overrides.id ?? randomUUID();
  const first_name = overrides.first_name ?? "Test";
  const last_name = overrides.last_name ?? "User";
  const email =
    overrides.email ??
    `test_${Math.random().toString(36).slice(2)}@example.com`;
  const password = overrides.password ?? "hash";
  const status = overrides.status ?? "active";
  const provider = overrides.provider ?? "default";
  const role = overrides.role ?? null;

  const [user] = await trx<DirectusUser[]>`
    INSERT INTO directus_users (id, first_name, last_name, email, password, status, provider, role)
    VALUES (${id}, ${first_name}, ${last_name}, ${email}, ${password}, ${status}, ${provider}, ${role})
    RETURNING *
  `;
  if (!user) throw new Error("Failed to insert user");
  return user;
}

export async function insertAccount(
  trx: TransactionSQL,
  owner: string
): Promise<Account> {
  const [account] = await trx<Account[]>`
    INSERT INTO account (owner)
    VALUES (${owner})
    RETURNING *
  `;
  if (!account) throw new Error("Failed to insert account");
  return account;
}

export async function insertProperty(
  trx: TransactionSQL,
  accountId: string,
  name = `Prop ${Math.random().toString(36).slice(2)}`
): Promise<Property> {
  const timezone = "America/Sao_Paulo";
  const [property] = await trx<Property[]>`
    INSERT INTO property (account, name, timezone)
    VALUES (${accountId}, ${name}, ${timezone})
    RETURNING *
  `;
  if (!property) throw new Error("Failed to insert property");
  return property;
}

export async function insertDevice(
  trx: TransactionSQL,
  model: Device["model"],
  identifier?: string
): Promise<Device> {
  const ident =
    identifier ??
    Math.random().toString(16).slice(2) + Math.random().toString(16).slice(2);

  const [device] = await trx<Device[]>`
    INSERT INTO device (model, identifier)
    VALUES (${model}, ${ident})
    RETURNING *
  `;
  if (!device) throw new Error("Failed to insert device");
  return device;
}

export async function insertPropertyDevice(
  trx: TransactionSQL,
  deviceId: string,
  propertyId: string,
  start: Date,
  end?: Date | null
): Promise<PropertyDevice> {
  const [pd] = await trx<PropertyDevice[]>`
    INSERT INTO property_device (device, property, start_date, end_date)
    VALUES (${deviceId}, ${propertyId}, ${start}, ${end ?? null})
    RETURNING *
  `;
  if (!pd) throw new Error("Failed to insert property_device");
  return pd;
}

export async function insertMeshMapping(
  trx: TransactionSQL,
  meshPdId: string,
  licPdId: string,
  start: Date,
  end?: Date | null
): Promise<MeshDeviceMapping> {
  const [mapping] = await trx<MeshDeviceMapping[]>`
    INSERT INTO mesh_device_mapping (mesh_property_device, lic_property_device, start_date, end_date)
    VALUES (${meshPdId}, ${licPdId}, ${start}, ${end ?? null})
    RETURNING *
  `;
  if (!mapping) throw new Error("Failed to insert mesh_device_mapping");
  return mapping;
}

export async function getDeviceModel(trx: TransactionSQL, deviceId: string) {
  const rows =
    await trx`SELECT model FROM device WHERE id = ${deviceId} LIMIT 1`;
  return (rows[0]?.model as string) ?? undefined;
}

export async function getCurrentMeshMappingId(
  trx: TransactionSQL,
  meshPdId: string
): Promise<string | null> {
  const rows = await trx`
    SELECT current_mesh_device_mapping
    FROM property_device
    WHERE id = ${meshPdId}
    LIMIT 1
  `;
  return (rows[0]?.current_mesh_device_mapping as string) ?? null;
}

export function daysAgo(n: number): Date {
  const d = new Date();
  d.setUTCDate(d.getUTCDate() - n);
  return d;
}

export function plusSeconds(date: Date, seconds: number): Date {
  const d = new Date(date.getTime());
  d.setUTCSeconds(d.getUTCSeconds() + seconds);
  return d;
}
