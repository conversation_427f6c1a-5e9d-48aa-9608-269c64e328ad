import { SQL } from "bun";

export const db = new SQL(
  process.env.TEST_POSTGRES_URL ||
    "postgres://postgres:@localhost:5432/irriga_mais?sslmode=prefer",
  {
    max: 20,
    idleTimeout: 30,
    connectionTimeout: 30,
  }
);

/**
 * Begin a transaction for a test, executes the function, and rolls back  .
 */
export async function runInTransaction<T>(
  fn: SQL.TransactionContextCallback<T>,
  trxOrDb?: SQL
) {
  const _db = trxOrDb ?? db;
  return _db.transaction(async (trx) => {
    try {
      const result = await fn(trx);
      return result;
    } finally {
      await trx`ROLLBACK;`;
    }
  });
}
